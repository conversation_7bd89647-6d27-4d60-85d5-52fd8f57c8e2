2025-05-14 13:51:51,005 - root - INFO - Successfully imported health tools
2025-05-14 13:51:51,005 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 13:51:51,005 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 13:51:51,046 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 13:51:51,046 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 13:51:51,046 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 13:51:51,092 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 13:56:39,148 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 13:58:26,440 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 13:58:26,491 - root - INFO - Original health data: {"Glucose": 159.0, "SpO2": 86.0, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": 133.0, "Blood Pressure (Diastolic)": 103.0, "Weight (BMI)": 32.3, "Temperature": 36.8, "Malaria": "Negative", "Widal Test": "Negative", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 8.0, "Waist Circumference": 120.0, "Fev": 23.0}
2025-05-14 13:58:26,492 - root - INFO - Health data received: {"Glucose": 159.0, "SpO2": 86.0, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": 133.0, "Blood Pressure (Diastolic)": 103.0, "Weight (BMI)": 32.3, "Temperature": 36.8, "Malaria": "Negative", "Widal Test": "Negative", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 8.0, "Waist Circumference": 120.0, "Fev": 23.0}
2025-05-14 13:58:26,502 - root - INFO - Processed health data: {"Glucose": 159.0, "SpO2": 86.0, "Blood Pressure (Systolic)": 133.0, "Blood Pressure (Diastolic)": 103.0, "Weight (BMI)": 32.3, "Temperature": 36.8, "Malaria": "Negative", "Widal Test": "Negative", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 8.0, "Waist Circumference": 120.0, "Fev": 23.0}
2025-05-14 13:58:26,507 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 13:58:26,507 - root - INFO - Health score report generated: {"Total Score": 31, "Health Status": "Poor", "Vitals Needing Improvement": "Glucose (Abnormal), SpO2 (Low), Blood Pressure (Systolic) (Abnormal), Blood Pressure (Diastolic) (Abnormal), Weight (BMI) (High), Hepatitis B (Positive), Fev (Low)", "Improvement Tips": "Consult a doctor about your glucose levels.. Improve oxygen saturation with breathing exercises.. Monitor systolic pressure with a doctor.. Monitor diastolic pressure with a doctor.. Reduce Weight (BMI) through proper lifestyle changes.. Seek medical attention for Hepatitis B.. Improve respiratory function with breathing therapy."}
2025-05-14 13:58:26,509 - root - INFO - Saved health score data for user 4a705e78-318d-4015-b601-cb496fc8a8fd
2025-05-14 14:10:10,596 - root - INFO - Successfully imported health tools
2025-05-14 14:10:10,597 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 14:10:10,598 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 14:10:10,630 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 14:10:10,630 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 14:10:10,631 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 14:10:10,665 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 14:12:46,473 - root - INFO - Successfully imported health tools
2025-05-14 14:12:46,474 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 14:12:46,474 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 14:12:46,505 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 14:12:46,506 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 14:12:46,506 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 14:12:46,540 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 14:14:29,717 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 14:17:38,842 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 14:17:38,901 - root - INFO - Original health data: {"Glucose": 129.0, "SpO2": 85.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 169.0, "Blood Pressure (Diastolic)": 120.0, "Weight (BMI)": 30.9, "Temperature": null, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": 176.5, "Fev": 85.0}
2025-05-14 14:17:38,905 - root - INFO - Health data received: {"Glucose": 129.0, "SpO2": 85.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 169.0, "Blood Pressure (Diastolic)": 120.0, "Weight (BMI)": 30.9, "Temperature": null, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": 176.5, "Fev": 85.0}
2025-05-14 14:17:38,908 - root - INFO - Processed health data: {"Glucose": 129.0, "SpO2": 85.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 169.0, "Blood Pressure (Diastolic)": 120.0, "Weight (BMI)": 30.9, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Waist Circumference": 176.5, "Fev": 85.0}
2025-05-14 14:17:38,916 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 14:17:38,917 - root - INFO - Health score report generated: {"Total Score": 20, "Health Status": "Poor", "Vitals Needing Improvement": ["Glucose (Abnormal)", "SpO2 (Low)", "Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Weight (BMI) (High)", "Malaria (Positive)"], "Improvement Tips": ["Consult a doctor about your glucose levels.", "Improve oxygen saturation with breathing exercises.", "Monitor systolic pressure with a doctor.", "Monitor diastolic pressure with a doctor.", "Reduce Weight (BMI) through proper lifestyle changes.", "Seek medical attention for Malaria."]}
2025-05-14 14:17:38,924 - root - INFO - Saved health score data for user 1edec388-03b6-4738-aa6f-29f79e65aa17
2025-05-14 14:24:50,164 - root - INFO - Successfully imported health tools
2025-05-14 14:24:50,166 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 14:24:50,166 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 14:24:50,197 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 14:24:50,198 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 14:24:50,199 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 14:24:50,250 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 14:44:15,954 - root - INFO - Successfully imported health tools
2025-05-14 14:44:15,970 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 14:44:15,996 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 14:44:16,082 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 14:44:16,098 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 14:44:16,098 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 14:44:16,158 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 14:45:54,040 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 14:46:18,865 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 14:46:18,916 - root - INFO - Original health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Negative", "Voluntary Serology": "Positive", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 23.0}
2025-05-14 14:46:18,916 - root - INFO - Health data received: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Negative", "Voluntary Serology": "Positive", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 23.0}
2025-05-14 14:46:18,916 - root - INFO - Processed health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Negative", "Voluntary Serology": "Positive", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 23.0}
2025-05-14 14:46:18,916 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 14:46:18,923 - root - INFO - Health score report generated: {"Total Score": 78, "Health Status": "Good", "Vitals Needing Improvement": ["Widal Test (Positive)", "Voluntary Serology (Positive)", "Fev (Low)"], "Improvement Tips": ["\ud83e\ude7a I'd recommend checking in with your doctor about your Widal Test result. They can provide the right guidance for you.", "\ud83e\ude7a I'd recommend checking in with your doctor about your Voluntary Serology result. They can provide the right guidance for you.", "\ud83e\udec1 Your lung function test (FEV) shows room for improvement. Regular breathing exercises can really help strengthen your lungs. Try taking slow, deep breaths for 5 minutes a few times a day. If you have asthma or other respiratory conditions, make sure you're following your treatment plan."]}
2025-05-14 14:46:18,952 - root - INFO - Saved health score data for user a1b94c7e-e14c-47b3-b003-5ead015cc91b
2025-05-14 15:26:11,931 - root - INFO - Successfully imported health tools
2025-05-14 15:26:11,931 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 15:26:11,939 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 15:26:11,993 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 15:26:11,993 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 15:26:11,995 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 15:26:12,047 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 15:26:57,316 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 15:28:16,225 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 15:28:16,333 - root - INFO - Original health data: {"Glucose": 117.0, "SpO2": null, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 150.0, "Blood Pressure (Diastolic)": 108.0, "Weight (BMI)": 38.5, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": null}
2025-05-14 15:28:16,340 - root - INFO - Health data received: {"Glucose": 117.0, "SpO2": null, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 150.0, "Blood Pressure (Diastolic)": 108.0, "Weight (BMI)": 38.5, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": null}
2025-05-14 15:28:16,340 - root - INFO - Processed health data: {"Glucose": 117.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 150.0, "Blood Pressure (Diastolic)": 108.0, "Weight (BMI)": 38.5, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0}
2025-05-14 15:28:16,347 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 15:28:16,359 - root - INFO - Health score report generated: {"Total Score": 40, "Health Status": "Poor", "Vitals Needing Improvement": ["Glucose (Moderately High)", "Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Weight (BMI) (High)", "Widal Test (Positive)", "Hepatitis B (Positive)"], "Improvement Tips": ["\ud83c\udf4e To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.", "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83e\ude7a Your test results for Widal Test, Hepatitis B require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-14 15:28:16,359 - root - INFO - Saved health score data for user b38e6b69-b8de-44d2-99e4-a7d63e421ae9
2025-05-14 15:29:36,434 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 15:29:36,448 - root - INFO - Retrieved context: ...
2025-05-14 15:29:36,448 - root - INFO - Added health data context to system prompt for user b38e6b69-b8de-44d2-99e4-a7d63e421ae9
2025-05-14 15:30:32,423 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 15:39:58,630 - root - INFO - Successfully imported health tools
2025-05-14 15:39:58,631 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 15:39:58,631 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 15:39:58,684 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 15:39:58,684 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 15:39:58,687 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 15:39:58,737 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 15:40:50,868 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 15:40:50,873 - root - INFO - Retrieved context: ...
2025-05-14 15:40:56,847 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 15:41:21,820 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 15:42:50,915 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 15:42:51,014 - root - INFO - Original health data: {"Glucose": 56.0, "SpO2": null, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 160.0, "Blood Pressure (Diastolic)": 120.0, "Weight (BMI)": 27.8, "Temperature": 35.5, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Negative", "Perfusion_index": null, "Waist Circumference": 80.0, "Fev": null}
2025-05-14 15:42:51,015 - root - INFO - Health data received: {"Glucose": 56.0, "SpO2": null, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 160.0, "Blood Pressure (Diastolic)": 120.0, "Weight (BMI)": 27.8, "Temperature": 35.5, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Negative", "Perfusion_index": null, "Waist Circumference": 80.0, "Fev": null}
2025-05-14 15:42:51,015 - root - INFO - Processed health data: {"Glucose": 56.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 160.0, "Blood Pressure (Diastolic)": 120.0, "Weight (BMI)": 27.8, "Temperature": 35.5, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Negative", "Waist Circumference": 80.0}
2025-05-14 15:42:51,021 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 15:42:51,021 - root - INFO - Health score report generated: {"Total Score": 29, "Health Status": "Poor", "Vitals Needing Improvement": ["Glucose (Abnormal)", "Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Weight (BMI) (Moderately High)", "Temperature (Abnormal)", "Widal Test (Positive)", "Hepatitis B (Positive)"], "Improvement Tips": ["\ud83e\ude7a Your glucose levels warrant a discussion with your healthcare provider who can recommend appropriate monitoring and management strategies.", "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83d\udeb6\u200d\u2640\ufe0f Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight.", "\ud83c\udf21\ufe0f Your temperature is running a bit low. Keep warm, stay hydrated, and monitor for any other symptoms that might accompany this reading.", "\ud83e\ude7a Your test results for Widal Test, Hepatitis B require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-14 15:42:51,031 - root - INFO - Saved health score data for user 252ef5d2-da50-4381-b65c-8df1910274f0
2025-05-14 15:43:54,188 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 15:43:54,189 - root - INFO - Retrieved context: ...
2025-05-14 15:43:54,190 - root - INFO - Added health data context to system prompt for user 252ef5d2-da50-4381-b65c-8df1910274f0
2025-05-14 15:45:11,821 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 15:45:11,822 - root - INFO - Detected primary intent: yes_confirmation with score 1.5
2025-05-14 15:47:41,084 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 15:47:41,087 - root - INFO - Retrieved context: ...
2025-05-14 15:47:41,088 - root - INFO - Added health data context to system prompt for user 252ef5d2-da50-4381-b65c-8df1910274f0
2025-05-14 15:49:06,346 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 15:49:06,347 - root - INFO - Detected primary intent: yes_confirmation with score 1.5
2025-05-14 16:02:01,533 - root - INFO - Processing vital signs: {"data": {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5}}
2025-05-14 16:02:01,533 - root - INFO - Vital signs monitoring result: ✅ Systolic blood pressure is normal. Your cardiovascular health is stable.
✅ Diastolic blood pressure is normal. Your cardiovascular health is stable.
✅ Heart rate is normal. Your cardiovascular health looks good.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
✅ Body temperature is normal. Your body is functioning well.
⚠️ BMI is high. This could indicate overweight or obesity. Consider a healthy diet and exercise.
2025-05-14 16:02:01,533 - root - INFO - Saved vital signs data for user test_user_123
2025-05-14 16:02:01,537 - root - INFO - Original health data: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:02:01,537 - root - INFO - Health data received: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:02:01,537 - root - INFO - Processed health data: {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:02:01,537 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 16:02:01,541 - root - INFO - Health score report generated: {"Total Score": 75, "Health Status": "Good", "Vitals Needing Improvement": ["Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Glucose (Moderately High)", "Weight (BMI) (Moderately High)"], "Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83c\udf4e To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.", "\ud83d\udeb6\u200d\u2640\ufe0f Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight."]}
2025-05-14 16:02:01,541 - root - INFO - Saved health score data for user test_user_123
2025-05-14 16:02:01,558 - root - INFO - Saved kidney function data for user test_user_123
2025-05-14 16:02:01,566 - root - ERROR - Error analyzing lipid profile: 'age'
2025-05-14 16:02:04,332 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:02:04,334 - root - INFO - Retrieved context: ...
2025-05-14 16:02:04,334 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 16:02:54,828 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:02:54,829 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 16:02:55,010 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:02:55,012 - root - INFO - Retrieved context: ...
2025-05-14 16:02:55,013 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 16:03:59,518 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:03:59,520 - root - INFO - Detected primary intent: yes_confirmation with score 2.0
2025-05-14 16:03:59,521 - root - INFO - Confirmation detected, using previous intent: health_consultation
2025-05-14 16:06:19,821 - root - INFO - Processing vital signs: {"data": {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5}}
2025-05-14 16:06:19,821 - root - INFO - Vital signs monitoring result: ✅ Systolic blood pressure is normal. Your cardiovascular health is stable.
✅ Diastolic blood pressure is normal. Your cardiovascular health is stable.
✅ Heart rate is normal. Your cardiovascular health looks good.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
✅ Body temperature is normal. Your body is functioning well.
⚠️ BMI is high. This could indicate overweight or obesity. Consider a healthy diet and exercise.
2025-05-14 16:06:19,827 - root - INFO - Saved vital signs data for user test_user_123
2025-05-14 16:06:19,836 - root - INFO - Original health data: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:06:19,836 - root - INFO - Health data received: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:06:19,836 - root - INFO - Processed health data: {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:06:19,836 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 16:06:19,836 - root - INFO - Health score report generated: {"Total Score": 75, "Health Status": "Good", "Vitals Needing Improvement": ["Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Glucose (Moderately High)", "Weight (BMI) (Moderately High)"], "Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83c\udf4e To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.", "\ud83d\udeb6\u200d\u2640\ufe0f Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight."]}
2025-05-14 16:06:19,836 - root - INFO - Saved health score data for user test_user_123
2025-05-14 16:06:19,847 - root - INFO - Saved kidney function data for user test_user_123
2025-05-14 16:06:19,853 - root - ERROR - Error analyzing lipid profile: 'age'
2025-05-14 16:06:20,511 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:06:20,513 - root - INFO - Retrieved context: ...
2025-05-14 16:06:20,514 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 16:08:23,584 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:08:23,586 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 16:08:23,774 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:08:23,774 - root - INFO - Retrieved context: ...
2025-05-14 16:08:23,774 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 16:10:28,640 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:10:28,640 - root - INFO - Detected primary intent: yes_confirmation with score 2.0
2025-05-14 16:10:28,640 - root - INFO - Confirmation detected, using previous intent: health_consultation
2025-05-14 16:16:38,581 - root - INFO - Successfully imported health tools
2025-05-14 16:16:38,583 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 16:16:38,585 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 16:16:38,630 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 16:16:38,630 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 16:16:38,632 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 16:16:38,709 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 16:17:40,503 - root - INFO - Processing vital signs: {"data": {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5}}
2025-05-14 16:17:40,503 - root - INFO - Vital signs monitoring result: ✅ Systolic blood pressure is normal. Your cardiovascular health is stable.
✅ Diastolic blood pressure is normal. Your cardiovascular health is stable.
✅ Heart rate is normal. Your cardiovascular health looks good.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
✅ Body temperature is normal. Your body is functioning well.
⚠️ BMI is high. This could indicate overweight or obesity. Consider a healthy diet and exercise.
2025-05-14 16:17:40,504 - root - INFO - Saved vital signs data for user test_user_123
2025-05-14 16:17:40,510 - root - INFO - Original health data: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:17:40,510 - root - INFO - Health data received: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:17:40,510 - root - INFO - Processed health data: {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:17:40,510 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 16:17:40,510 - root - INFO - Health score report generated: {"Total Score": 75, "Health Status": "Good", "Vitals Needing Improvement": ["Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Glucose (Moderately High)", "Weight (BMI) (Moderately High)"], "Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83c\udf4e To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.", "\ud83d\udeb6\u200d\u2640\ufe0f Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight."]}
2025-05-14 16:17:40,510 - root - INFO - Saved health score data for user test_user_123
2025-05-14 16:17:40,517 - root - INFO - Saved kidney function data for user test_user_123
2025-05-14 16:17:40,526 - root - ERROR - Error analyzing lipid profile: 'age'
2025-05-14 16:17:42,738 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:17:42,740 - root - INFO - Retrieved context: ...
2025-05-14 16:17:42,740 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 16:22:54,860 - root - INFO - Successfully imported health tools
2025-05-14 16:22:54,861 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 16:22:54,863 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 16:22:54,911 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 16:22:54,912 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 16:22:54,912 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 16:22:54,943 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 16:23:21,896 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 16:23:40,121 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 16:23:40,177 - root - INFO - Original health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 16:23:40,182 - root - INFO - Health data received: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 16:23:40,204 - root - INFO - Processed health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 16:23:40,212 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 16:23:40,212 - root - INFO - Health score report generated: {"Total Score": 78, "Health Status": "Good", "Vitals Needing Improvement": ["Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)"], "Improvement Tips": ["\ud83e\ude7a Your test results for Malaria, Widal Test, Hepatitis B require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-14 16:23:40,218 - root - INFO - Saved health score data for user 27c40c82-d1c9-497f-b6d5-89c95cb4e128
2025-05-14 16:24:10,983 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:24:10,996 - root - INFO - Retrieved context: ...
2025-05-14 16:24:10,996 - root - INFO - Added health data context to system prompt for user 27c40c82-d1c9-497f-b6d5-89c95cb4e128
2025-05-14 16:24:40,599 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:24:40,600 - root - INFO - Detected primary intent: yes_confirmation with score 1.5
2025-05-14 16:36:48,948 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 16:37:15,689 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 16:37:15,768 - root - INFO - Original health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 51.0, "Fev": 23.0}
2025-05-14 16:37:15,779 - root - INFO - Health data received: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 51.0, "Fev": 23.0}
2025-05-14 16:37:15,780 - root - INFO - Processed health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 51.0, "Fev": 23.0}
2025-05-14 16:37:15,781 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 16:37:15,781 - root - INFO - Health score report generated: {"Total Score": 72, "Health Status": "Good", "Vitals Needing Improvement": ["Malaria (Positive)", "Hepatitis B (Positive)", "Fev (Low)"], "Improvement Tips": ["\ud83e\udec1 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor.", "\ud83e\ude7a Your test results for Malaria, Hepatitis B require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-14 16:37:15,784 - root - INFO - Saved health score data for user 24fc6413-e0e6-455d-b9a7-1703684de1f5
2025-05-14 16:38:05,739 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:38:05,740 - root - INFO - Retrieved context: ...
2025-05-14 16:38:05,740 - root - INFO - Added health data context to system prompt for user 24fc6413-e0e6-455d-b9a7-1703684de1f5
2025-05-14 16:38:48,917 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:38:48,918 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 16:39:29,770 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:39:29,772 - root - INFO - Retrieved context: ...
2025-05-14 16:39:29,772 - root - INFO - Added health data context to system prompt for user 24fc6413-e0e6-455d-b9a7-1703684de1f5
2025-05-14 16:41:45,805 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:41:45,806 - root - INFO - Detected primary intent: yes_confirmation with score 2.0
2025-05-14 16:41:45,807 - root - INFO - Confirmation detected, using previous intent: health_consultation
2025-05-14 16:41:45,807 - root - ERROR - Failed to process agent query: argument of type 'coroutine' is not iterable
2025-05-14 16:41:45,809 - root - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\MCP\agent_server.py", line 837, in process_agent_query
    if "error" in consultation_result:
TypeError: argument of type 'coroutine' is not iterable

2025-05-14 16:41:45,809 - root - ERROR - Error processing query: Failed to process agent query: argument of type 'coroutine' is not iterable
2025-05-14 16:41:45,811 - root - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\MCP\agent_server.py", line 837, in process_agent_query
    if "error" in consultation_result:
TypeError: argument of type 'coroutine' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\MCP\agent_server.py", line 1132, in get_response
    result = process_agent_query(query, user_id, model)
  File "C:\Users\<USER>\OneDrive\Desktop\MCP\agent_server.py", line 989, in process_agent_query
    raise Exception(error_msg)
Exception: Failed to process agent query: argument of type 'coroutine' is not iterable

2025-05-14 16:43:38,112 - root - INFO - Successfully imported health tools
2025-05-14 16:43:38,112 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 16:43:38,117 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 16:43:38,154 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 16:43:38,155 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 16:43:38,155 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 16:43:38,189 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 16:43:53,186 - root - INFO - Processing vital signs: {"data": {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5}}
2025-05-14 16:43:53,186 - root - INFO - Vital signs monitoring result: ✅ Systolic blood pressure is normal. Your cardiovascular health is stable.
✅ Diastolic blood pressure is normal. Your cardiovascular health is stable.
✅ Heart rate is normal. Your cardiovascular health looks good.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
✅ Body temperature is normal. Your body is functioning well.
⚠️ BMI is high. This could indicate overweight or obesity. Consider a healthy diet and exercise.
2025-05-14 16:43:53,190 - root - INFO - Saved vital signs data for user test_user_123
2025-05-14 16:43:53,193 - root - INFO - Original health data: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:43:53,193 - root - INFO - Health data received: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:43:53,193 - root - INFO - Processed health data: {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:43:53,193 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 16:43:53,196 - root - INFO - Health score report generated: {"Total Score": 75, "Health Status": "Good", "Vitals Needing Improvement": ["Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Glucose (Moderately High)", "Weight (BMI) (Moderately High)"], "Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83c\udf4e To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.", "\ud83d\udeb6\u200d\u2640\ufe0f Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight."]}
2025-05-14 16:43:53,196 - root - INFO - Saved health score data for user test_user_123
2025-05-14 16:43:53,204 - root - INFO - Saved kidney function data for user test_user_123
2025-05-14 16:43:53,209 - root - ERROR - Error analyzing lipid profile: 'age'
2025-05-14 16:43:53,665 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:43:53,666 - root - INFO - Retrieved context: ...
2025-05-14 16:43:53,666 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 16:44:54,893 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:44:54,896 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 16:45:21,503 - root - INFO - Processing vital signs: {"data": {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5}}
2025-05-14 16:45:21,503 - root - INFO - Vital signs monitoring result: ✅ Systolic blood pressure is normal. Your cardiovascular health is stable.
✅ Diastolic blood pressure is normal. Your cardiovascular health is stable.
✅ Heart rate is normal. Your cardiovascular health looks good.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
✅ Body temperature is normal. Your body is functioning well.
⚠️ BMI is high. This could indicate overweight or obesity. Consider a healthy diet and exercise.
2025-05-14 16:45:21,503 - root - INFO - Saved vital signs data for user test_user_123
2025-05-14 16:45:21,512 - root - INFO - Original health data: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:45:21,512 - root - INFO - Health data received: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:45:21,514 - root - INFO - Processed health data: {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 16:45:21,514 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 16:45:21,514 - root - INFO - Health score report generated: {"Total Score": 75, "Health Status": "Good", "Vitals Needing Improvement": ["Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Glucose (Moderately High)", "Weight (BMI) (Moderately High)"], "Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83c\udf4e To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.", "\ud83d\udeb6\u200d\u2640\ufe0f Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight."]}
2025-05-14 16:45:21,514 - root - INFO - Saved health score data for user test_user_123
2025-05-14 16:45:21,528 - root - INFO - Saved kidney function data for user test_user_123
2025-05-14 16:45:21,535 - root - ERROR - Error analyzing lipid profile: 'age'
2025-05-14 16:45:22,194 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:45:22,195 - root - INFO - Retrieved context: ...
2025-05-14 16:45:22,196 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 16:50:04,279 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:50:04,281 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 16:50:27,730 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:50:27,732 - root - INFO - Retrieved context: ...
2025-05-14 16:50:27,732 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 16:53:17,385 - root - INFO - Successfully imported health tools
2025-05-14 16:53:17,394 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 16:53:17,394 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 16:53:17,421 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 16:53:17,421 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 16:53:17,421 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 16:53:17,451 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 16:53:50,614 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 16:54:17,206 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 16:54:17,262 - root - INFO - Original health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 16:54:17,292 - root - INFO - Health data received: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 16:54:17,294 - root - INFO - Processed health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 16:54:17,297 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 16:54:17,297 - root - INFO - Health score report generated: {"Total Score": 78, "Health Status": "Good", "Vitals Needing Improvement": ["Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)"], "Improvement Tips": ["\ud83e\ude7a Your test results for Malaria, Widal Test, Hepatitis B require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-14 16:54:17,297 - root - INFO - Saved health score data for user e7da4cb8-8d69-4591-973e-971008ec1d89
2025-05-14 16:55:04,013 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:55:04,018 - root - INFO - Retrieved context: ...
2025-05-14 16:55:04,018 - root - INFO - Added health data context to system prompt for user e7da4cb8-8d69-4591-973e-971008ec1d89
2025-05-14 16:55:37,050 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:55:37,052 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 16:55:54,706 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 16:55:54,708 - root - INFO - Retrieved context: ...
2025-05-14 16:55:54,709 - root - INFO - Added health data context to system prompt for user e7da4cb8-8d69-4591-973e-971008ec1d89
2025-05-14 16:57:18,557 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 16:57:18,557 - root - INFO - Detected primary intent: yes_confirmation with score 2.0
2025-05-14 16:57:18,558 - root - INFO - Confirmation detected, using previous intent: health_consultation
2025-05-14 17:00:58,633 - root - INFO - Successfully imported health tools
2025-05-14 17:00:58,641 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 17:00:58,641 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 17:00:58,673 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 17:00:58,678 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 17:00:58,679 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 17:00:58,711 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 17:01:30,748 - root - INFO - Processing vital signs: {"data": {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5}}
2025-05-14 17:01:30,748 - root - INFO - Vital signs monitoring result: ✅ Systolic blood pressure is normal. Your cardiovascular health is stable.
✅ Diastolic blood pressure is normal. Your cardiovascular health is stable.
✅ Heart rate is normal. Your cardiovascular health looks good.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
✅ Body temperature is normal. Your body is functioning well.
⚠️ BMI is high. This could indicate overweight or obesity. Consider a healthy diet and exercise.
2025-05-14 17:01:30,755 - root - INFO - Saved vital signs data for user test_user_123
2025-05-14 17:01:30,762 - root - INFO - Original health data: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 17:01:30,764 - root - INFO - Health data received: {"Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "ECG (Heart Rate)": 75, "SpO2": 97, "Glucose": 110, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 17:01:30,765 - root - INFO - Processed health data: {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5, "Malaria": "Negative", "Hepatitis B": "Negative", "Widal Test": "Negative", "Voluntary Serology": "Negative"}
2025-05-14 17:01:30,765 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 17:01:30,765 - root - INFO - Health score report generated: {"Total Score": 75, "Health Status": "Good", "Vitals Needing Improvement": ["Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Glucose (Moderately High)", "Weight (BMI) (Moderately High)"], "Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83c\udf4e To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.", "\ud83d\udeb6\u200d\u2640\ufe0f Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight."]}
2025-05-14 17:01:30,765 - root - INFO - Saved health score data for user test_user_123
2025-05-14 17:01:30,772 - root - INFO - Saved kidney function data for user test_user_123
2025-05-14 17:01:30,789 - root - ERROR - Error analyzing lipid profile: 'age'
2025-05-14 17:01:31,393 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 17:01:31,394 - root - INFO - Retrieved context: ...
2025-05-14 17:01:31,395 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 17:02:31,688 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 17:02:31,689 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 17:02:31,870 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 17:02:31,872 - root - INFO - Retrieved context: ...
2025-05-14 17:02:31,872 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 17:03:47,729 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 17:03:47,730 - root - INFO - Detected primary intent: yes_confirmation with score 2.0
2025-05-14 17:03:47,730 - root - INFO - Confirmation detected, using previous intent: health_consultation
2025-05-14 17:05:58,313 - root - INFO - Successfully imported health tools
2025-05-14 17:05:58,316 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 17:05:58,316 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 17:05:58,346 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 17:05:58,346 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 17:05:58,347 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 17:05:58,450 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 17:06:54,861 - root - INFO - Processing vital signs: {"data": {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5}}
2025-05-14 17:06:54,861 - root - INFO - Vital signs monitoring result: ✅ Systolic blood pressure is normal. Your cardiovascular health is stable.
✅ Diastolic blood pressure is normal. Your cardiovascular health is stable.
✅ Heart rate is normal. Your cardiovascular health looks good.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
✅ Body temperature is normal. Your body is functioning well.
⚠️ BMI is high. This could indicate overweight or obesity. Consider a healthy diet and exercise.
2025-05-14 17:06:54,865 - root - INFO - Saved vital signs data for user test_user_123
2025-05-14 17:07:43,955 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 17:07:43,960 - root - INFO - Retrieved context: ...
2025-05-14 17:07:43,961 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 17:08:56,596 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 17:08:56,598 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 17:09:26,366 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 17:09:26,368 - root - INFO - Retrieved context: ...
2025-05-14 17:09:26,368 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 17:10:37,263 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 17:10:37,263 - root - INFO - Detected primary intent: yes_confirmation with score 2.0
2025-05-14 17:10:37,263 - root - INFO - Confirmation detected, using previous intent: health_consultation
2025-05-14 17:16:19,190 - root - INFO - Successfully imported health tools
2025-05-14 17:16:19,190 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 17:16:19,190 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 17:16:19,232 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 17:16:19,233 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 17:16:19,233 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 17:16:19,273 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 17:16:42,802 - root - INFO - Processing vital signs: {"data": {"Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "ECG (Heart Rate)": 75.0, "SpO2": 97.0, "Glucose": 110.0, "Temperature": 37.0, "Weight (BMI)": 26.5}}
2025-05-14 17:16:42,802 - root - INFO - Vital signs monitoring result: ✅ Systolic blood pressure is normal. Your cardiovascular health is stable.
✅ Diastolic blood pressure is normal. Your cardiovascular health is stable.
✅ Heart rate is normal. Your cardiovascular health looks good.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
✅ Body temperature is normal. Your body is functioning well.
⚠️ BMI is high. This could indicate overweight or obesity. Consider a healthy diet and exercise.
2025-05-14 17:16:42,804 - root - INFO - Saved vital signs data for user test_user_123
2025-05-14 17:17:04,123 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 17:17:04,128 - root - INFO - Retrieved context: ...
2025-05-14 17:17:04,128 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 17:17:37,451 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 17:17:37,451 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 17:18:19,138 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 17:18:19,141 - root - INFO - Retrieved context: ...
2025-05-14 17:18:19,141 - root - INFO - Added health data context to system prompt for user test_user_123
2025-05-14 17:19:03,377 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 17:19:03,377 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 17:20:10,601 - root - INFO - Successfully imported health tools
2025-05-14 17:20:10,604 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-14 17:20:10,604 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-14 17:20:10,640 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-14 17:20:10,640 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-14 17:20:10,640 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-14 17:20:10,684 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-14 17:20:51,901 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 17:21:51,517 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 17:21:51,572 - root - INFO - Original health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 35.1, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 10.5, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 17:21:51,604 - root - INFO - Health data received: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 35.1, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 10.5, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 17:21:51,612 - root - INFO - Processed health data: {"Glucose": 100.0, "SpO2": 98.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 35.1, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 10.5, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 17:21:51,616 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 17:21:51,620 - root - INFO - Health score report generated: {"Total Score": 62, "Health Status": "Fair", "Vitals Needing Improvement": ["Temperature (Abnormal)", "Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)"], "Improvement Tips": ["\ud83c\udf21\ufe0f Your temperature is running a bit low. Keep warm, stay hydrated, and monitor for any other symptoms that might accompany this reading.", "\ud83e\ude7a Your test results for Malaria, Widal Test, Hepatitis B require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-14 17:21:51,621 - root - INFO - Saved health score data for user d5da4040-2750-47b0-8474-8115547e1d1c
2025-05-14 17:25:28,624 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 17:25:28,630 - root - INFO - Retrieved context: ...
2025-05-14 17:25:28,630 - root - INFO - Added health data context to system prompt for user d5da4040-2750-47b0-8474-8115547e1d1c
2025-05-14 17:25:53,167 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 17:25:53,168 - root - INFO - Detected primary intent: health_consultation with score 2.5
2025-05-14 17:33:07,711 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 17:33:22,408 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-14 17:33:22,469 - root - INFO - Original health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 17:33:22,472 - root - INFO - Health data received: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 17:33:22,472 - root - INFO - Processed health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Positive", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-14 17:33:22,472 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-14 17:33:22,476 - root - INFO - Health score report generated: {"Total Score": 78, "Health Status": "Good", "Vitals Needing Improvement": ["Widal Test (Positive)", "Hepatitis B (Positive)"], "Improvement Tips": ["\ud83e\ude7a Your test results for Widal Test, Hepatitis B require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-14 17:33:22,478 - root - INFO - Saved health score data for user 5cda1b61-2873-488e-b887-4cb6184550e9
2025-05-14 17:37:03,907 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-14 17:37:03,909 - root - INFO - Retrieved context: ...
2025-05-14 17:37:03,909 - root - INFO - Added health data context to system prompt for user 5cda1b61-2873-488e-b887-4cb6184550e9
2025-05-14 17:37:23,668 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-14 17:37:23,668 - root - INFO - Detected primary intent: health_consultation with score 2.0
2025-05-16 03:49:38,241 - root - INFO - Successfully imported health tools
2025-05-16 03:49:38,241 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 03:49:38,241 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 03:49:38,275 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 03:49:38,276 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 03:49:38,276 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 03:49:38,313 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 03:54:42,106 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-16 03:54:42,115 - root - INFO - Retrieved context: ...
2025-05-16 03:54:57,805 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-16 08:03:06,673 - root - INFO - Processing lung capacity data: {"FEV1": 73.0, "FVC": 69.5, "FEV1_FVC_ratio": 1.0, "PEF": 110.0, "FEF25_75": 102.5, "Age": 40, "Sex": "Male", "Smoking_Status": "Non-smoker"}
2025-05-16 08:03:06,674 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 73.0% of predicted value, indicating mild airflow limitation.", "FVC is 69.5% of predicted value, indicating moderate restrictive pattern.", "FEV1/FVC ratio is 1.0 with reduced FVC, suggesting restrictive lung disease.", "PEF is 110.0% of predicted value, which is within normal range.", "FEF25-75 is 102.5% of predicted value, which is within normal range."], "respiratory_risk_level": "Moderate", "potential_conditions": ["Possible restrictive lung disease"], "recommendations": ["\ud83e\udec1 Consider full pulmonary function testing including lung volumes and diffusion capacity.", "\ud83e\ude7b Chest imaging may be indicated to evaluate for interstitial lung disease.", "\ud83d\udc68\u200d\u2695\ufe0f Consult with a pulmonologist for further evaluation.", "\ud83d\udca8 Practice breathing exercises to improve lung function.", "\ud83c\udfc3 Regular aerobic exercise can help improve respiratory capacity.", "\ud83d\ude37 Avoid respiratory irritants and air pollution when possible."], "confidence_level": "High", "missing_parameters": []}
2025-05-16 08:03:06,676 - root - INFO - Saved lung capacity data for user c44a8c6e-a60b-42e6-af68-af6ca17b3543
2025-05-16 08:04:47,408 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-16 08:04:47,410 - root - INFO - Retrieved context: ...
2025-05-16 08:04:47,410 - root - INFO - Added health data context to system prompt for user c44a8c6e-a60b-42e6-af68-af6ca17b3543
2025-05-16 08:05:12,886 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-16 08:17:15,902 - root - INFO - Successfully imported health tools
2025-05-16 08:17:15,902 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 08:17:15,902 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 08:17:15,933 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 08:17:15,933 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 08:17:15,933 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 08:17:15,972 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 08:19:11,355 - root - INFO - Processing lung capacity data: {"FEV1": 73.0, "FVC": 122.0, "FEV1_FVC_ratio": 0.5, "PEF": 70.0, "FEF25_75": 114.0, "Age": 40, "Sex": "Male", "Height": 120.0, "Race": "African American", "Smoking_Status": "Non-smoker"}
2025-05-16 08:19:11,357 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 73.0% of predicted value (0.78L, expected 1.07L)", "FVC is 122.0% of predicted value (1.69L, expected 1.39L)", "PEF is 70.0% of predicted value (273.67L/min, expected 390.96L/min)", "Based on your age, height, and demographics, your expected values are: FEV1 1.07L, FVC 1.39L, PEF 390.96L/min", "FEV1 is 73.0% of predicted value, indicating mild airflow limitation.", "FVC is 122.0% of predicted value, which is within normal range.", "FEV1/FVC ratio is 0.5, indicating obstructive lung disease.", "Pattern may be consistent with asthma.", "PEF is 70.0% of predicted value, indicating reduced peak flow.", "FEF25-75 is 114.0% of predicted value, which is within normal range."], "respiratory_risk_level": "High", "potential_conditions": ["Obstructive lung disease", "Possible asthma"], "recommendations": ["\ud83e\udec1 Consider pulmonary function testing with bronchodilator response to confirm asthma diagnosis.", "\ud83e\uddea Allergy testing may help identify triggers.", "\ud83d\udc68\u200d\u2695\ufe0f Consult with a pulmonologist or allergist for asthma management.", "\ud83d\udca8 Practice breathing exercises to improve lung function.", "\ud83c\udfc3 Regular aerobic exercise can help improve respiratory capacity.", "\ud83d\ude37 Avoid respiratory irritants and air pollution when possible."], "confidence_level": "High", "missing_parameters": []}
2025-05-16 08:19:11,362 - root - INFO - Saved lung capacity data for user 026ffdf2-62f2-43e9-8462-0d65486e03f3
2025-05-16 09:06:13,364 - root - INFO - Successfully imported health tools
2025-05-16 09:06:13,364 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 09:06:13,364 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 09:06:13,396 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 09:06:13,396 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 09:06:13,396 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 09:06:13,416 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 09:09:26,755 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-16 09:10:42,730 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-16 09:10:42,788 - root - INFO - Original health data: {"Glucose": 147.0, "SpO2": 98.0, "ECG (Heart Rate)": 195.0, "Blood Pressure (Systolic)": 127.0, "Blood Pressure (Diastolic)": 108.0, "Weight (BMI)": 32.6, "Temperature": 40.8, "Malaria": "Negative", "Widal Test": "Positive", "Hepatitis B": "Negative", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 57.5}
2025-05-16 09:10:42,813 - root - INFO - Health data received: {"Glucose": 147.0, "SpO2": 98.0, "ECG (Heart Rate)": 195.0, "Blood Pressure (Systolic)": 127.0, "Blood Pressure (Diastolic)": 108.0, "Weight (BMI)": 32.6, "Temperature": 40.8, "Malaria": "Negative", "Widal Test": "Positive", "Hepatitis B": "Negative", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 57.5}
2025-05-16 09:10:42,820 - root - INFO - Processed health data: {"Glucose": 147.0, "SpO2": 98.0, "ECG (Heart Rate)": 195.0, "Blood Pressure (Systolic)": 127.0, "Blood Pressure (Diastolic)": 108.0, "Weight (BMI)": 32.6, "Temperature": 40.8, "Malaria": "Negative", "Widal Test": "Positive", "Hepatitis B": "Negative", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 57.5}
2025-05-16 09:10:42,827 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-16 09:10:42,830 - root - INFO - Health score report generated: {"Total Score": 28, "Health Status": "Poor", "Vitals Needing Improvement": ["Glucose (Abnormal)", "ECG (Heart Rate) (Abnormal)", "Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Weight (BMI) (High)", "Temperature (Abnormal)", "Widal Test (Positive)", "Fev (Low)"], "Improvement Tips": ["\ud83e\ude7a Your glucose levels warrant a discussion with your healthcare provider who can recommend appropriate monitoring and management strategies.", "\u2764\ufe0f For your elevated heart rate, try practicing mindfulness meditation, limiting caffeine, and ensuring adequate rest. If it remains consistently high, consult your doctor.", "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83d\udcaa Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle.", "\ud83c\udf21\ufe0f For your elevated temperature, ensure you're well-hydrated, rest adequately, and monitor for any changes. If it persists beyond 48 hours, consult your doctor.", "\ud83e\udec1 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor.", "\ud83e\ude7a Your test results for Widal Test require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-16 09:10:42,840 - root - INFO - Saved health score data for user 7b2b7570-674a-4d90-800c-019740f94245
2025-05-16 09:12:32,328 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-16 09:12:32,335 - root - INFO - Retrieved context: ...
2025-05-16 09:12:32,335 - root - INFO - Added health data context to system prompt for user 7b2b7570-674a-4d90-800c-019740f94245
2025-05-16 09:13:43,660 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-16 09:16:01,486 - root - INFO - Processing lung capacity data: {"FEV1": 75.0, "FVC": 86.0, "FEV1_FVC_ratio": 0.3, "PEF": 65.0, "FEF25_75": 67.5, "Age": 40, "Sex": "Male", "Height": 120.0, "Race": "African American", "Smoking_Status": "Non-smoker"}
2025-05-16 09:16:01,486 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 75.0% of predicted value (0.80L, expected 1.07L)", "FVC is 86.0% of predicted value (1.19L, expected 1.39L)", "PEF is 65.0% of predicted value (254.12L/min, expected 390.96L/min)", "Based on your age, height, and demographics, your expected values are: FEV1 1.07L, FVC 1.39L, PEF 390.96L/min", "FEV1 is 75.0% of predicted value, indicating mild airflow limitation.", "FVC is 86.0% of predicted value, which is within normal range.", "FEV1/FVC ratio is 0.3, indicating obstructive lung disease.", "Pattern may be consistent with asthma.", "PEF is 65.0% of predicted value, indicating reduced peak flow.", "FEF25-75 is 67.5% of predicted value, which is within normal range."], "respiratory_risk_level": "High", "potential_conditions": ["Obstructive lung disease", "Possible asthma"], "recommendations": ["\ud83e\udec1 Consider pulmonary function testing with bronchodilator response to confirm asthma diagnosis.", "\ud83e\uddea Allergy testing may help identify triggers.", "\ud83d\udc68\u200d\u2695\ufe0f Consult with a pulmonologist or allergist for asthma management.", "\ud83d\udca8 Practice breathing exercises to improve lung function.", "\ud83c\udfc3 Regular aerobic exercise can help improve respiratory capacity.", "\ud83d\ude37 Avoid respiratory irritants and air pollution when possible."], "confidence_level": "High", "missing_parameters": [], "doctor_summary": "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: I'm seeing some significant concerns with your lung function that we should address. The test provided comprehensive data, so I'm quite confident in this assessment. Based on these patterns, I'm seeing indicators that could be associated with Obstructive lung disease, Possible asthma. Remember, these are preliminary findings based on your spirometry results, not a definitive diagnosis."}
2025-05-16 09:16:01,486 - root - INFO - Saved lung capacity data for user 7b2b7570-674a-4d90-800c-019740f94245
2025-05-16 09:38:29,463 - root - INFO - Saved lipid profile data for user bd15ba32-df39-4b38-ae2f-eb3c3cd38835
2025-05-16 09:40:27,658 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-16 09:40:27,659 - root - INFO - Retrieved context: ...
2025-05-16 09:40:27,659 - root - INFO - Added health data context to system prompt for user bd15ba32-df39-4b38-ae2f-eb3c3cd38835
2025-05-16 09:41:22,999 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-16 09:52:16,883 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-16 09:52:16,883 - root - INFO - Retrieved context: ...
2025-05-16 09:52:16,883 - root - INFO - Added health data context to system prompt for user bd15ba32-df39-4b38-ae2f-eb3c3cd38835
2025-05-16 09:53:20,807 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-16 09:53:20,808 - root - INFO - Detected primary intent: recommendation with score 1.0
2025-05-16 10:47:05,279 - root - INFO - Successfully imported health tools
2025-05-16 10:47:05,280 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 10:47:05,280 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 10:47:05,319 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 10:47:05,320 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 10:47:05,321 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 10:47:05,356 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 10:48:25,816 - root - INFO - Processing test results data: {"malaria": "Positive", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 10:48:25,816 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T10:48:25.816193", "tests_analyzed": ["Malaria"], "interpretation": ["\u26a0\ufe0f **Malaria detected.** This requires prompt medical treatment."], "recommendations": ["\ud83c\udfe5 See a doctor within 24 hours for appropriate antimalarial medication.", "\ud83c\udf21\ufe0f Monitor for fever, chills, headache, and other symptoms.", "\ud83d\udea8 Due to the urgency of your results, please seek medical attention immediately."], "urgency_level": "High", "flagged_results": ["Malaria (unspecified)"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some concerning results that require prompt medical attention. Specifically, I'm concerned about: Malaria (unspecified). Malaria is a serious infection that requires proper treatment. The good news is that with appropriate medication, most people recover completely. "}
2025-05-16 10:48:25,816 - root - INFO - Saved test results data for user 2c062cca-154e-4789-8f22-d878ed2bd849
2025-05-16 15:10:52,795 - root - INFO - Successfully imported health tools
2025-05-16 15:10:52,795 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 15:10:52,808 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 15:10:52,826 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 15:10:52,826 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 15:10:52,826 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 15:10:52,850 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 15:29:11,648 - root - INFO - Processing test results data: {"malaria": "Negative", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 15:29:11,648 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T15:29:11.648086", "tests_analyzed": ["Malaria"], "interpretation": ["\u2705 **Malaria test is negative.** No malaria parasites were detected in your blood sample.", "**Conclusion:** You do not have malaria based on this test."], "recommendations": ["\ud83d\udee1\ufe0f Continue using preventive measures in malaria-endemic areas (bed nets, insect repellent, etc.).", "\ud83c\udf21\ufe0f If you develop fever or other symptoms, consider retesting as early infections may not be detected."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. Your malaria test is negative. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 15:29:11,668 - root - INFO - Saved test results data for user dfc68cc9-6b9c-4572-9b2a-2d064283bf71
2025-05-16 15:53:34,528 - root - INFO - Successfully imported health tools
2025-05-16 15:53:34,555 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 15:53:34,570 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 15:53:34,634 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 15:53:34,665 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 15:53:34,666 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 15:53:34,711 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 15:57:19,374 - root - INFO - Processing test results data: {"malaria": "Negative", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 15:57:19,374 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T15:57:19.374402", "tests_analyzed": ["Malaria"], "interpretation": ["\u2705 **Malaria test is negative.** No malaria parasites were detected in your blood sample.", "**Conclusion:** You do not have malaria based on this test."], "recommendations": ["\ud83d\udee1\ufe0f Continue using preventive measures in malaria-endemic areas (bed nets, insect repellent, etc.).", "\ud83c\udf21\ufe0f If you develop fever or other symptoms, consider retesting as early infections may not be detected."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. Your malaria test is negative. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 15:57:19,379 - root - INFO - Saved test results data for user b40915f4-8044-46c7-8bcf-310fa85b5752
2025-05-16 15:58:43,087 - root - INFO - Processing test results data: {"malaria": "Positive", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 15:58:43,089 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T15:58:43.089979", "tests_analyzed": ["Malaria"], "interpretation": ["\u26a0\ufe0f **Malaria detected.** This requires prompt medical treatment.", "**Conclusion:** You have malaria (species unidentified)."], "recommendations": ["\ud83c\udfe5 See a doctor within 24 hours for appropriate antimalarial medication.", "\ud83c\udf21\ufe0f Monitor for fever, chills, headache, and other symptoms.", "\ud83d\udea8 Due to the urgency of your results, please seek medical attention immediately."], "urgency_level": "High", "flagged_results": ["Malaria (unspecified)"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some concerning results that require prompt medical attention. Specifically, I'm concerned about: Malaria (unspecified). You have malaria, which requires prompt treatment with appropriate antimalarial medication. With proper treatment, most people recover completely. "}
2025-05-16 15:58:43,104 - root - INFO - Saved test results data for user b40915f4-8044-46c7-8bcf-310fa85b5752
2025-05-16 16:08:11,857 - root - INFO - Processing test results data: {"malaria": "Unknown", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 16:08:11,858 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T16:08:11.858917", "tests_analyzed": ["Malaria"], "interpretation": ["\u2753 **Malaria test result is unknown or inconclusive.** The test did not provide a clear positive or negative result.", "**Conclusion:** Unable to determine if you have malaria. Further testing is recommended."], "recommendations": ["\ud83d\udd2c Consider repeating the malaria test for a definitive result.", "\ud83c\udfe5 If you have symptoms like fever, chills, or headache, consult a healthcare provider regardless of this test result."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 16:08:11,858 - root - INFO - Saved test results data for user b40915f4-8044-46c7-8bcf-310fa85b5752
2025-05-16 16:35:58,583 - root - INFO - Processing test results data: {"malaria": "Unknown", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 16:35:58,584 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T16:35:58.584493", "tests_analyzed": ["Malaria"], "interpretation": ["\u2753 **Malaria test result is unknown or inconclusive.** The test did not provide a clear positive or negative result.", "**Conclusion:** Unable to determine if you have malaria. Further testing is recommended."], "recommendations": ["\ud83d\udd2c Consider repeating the malaria test for a definitive result.", "\ud83c\udfe5 If you have symptoms like fever, chills, or headache, consult a healthcare provider regardless of this test result."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 16:35:58,586 - root - INFO - Saved test results data for user b40915f4-8044-46c7-8bcf-310fa85b5752
2025-05-16 16:53:51,139 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Unknown", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 16:53:51,194 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T16:53:51.194174", "tests_analyzed": ["Malaria"], "interpretation": ["\u2753 **Malaria test result is unknown or inconclusive.** The test did not provide a clear positive or negative result.", "**Conclusion:** Unable to determine if you have malaria. Further testing is recommended."], "recommendations": ["\ud83d\udd2c Consider repeating the malaria test for a definitive result.", "\ud83c\udfe5 If you have symptoms like fever, chills, or headache, consult a healthcare provider regardless of this test result."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 16:53:51,215 - root - INFO - Saved test results data for user b40915f4-8044-46c7-8bcf-310fa85b5752
2025-05-16 17:07:56,618 - root - INFO - Successfully imported health tools
2025-05-16 17:07:56,623 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 17:07:56,624 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 17:07:56,673 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 17:07:56,674 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 17:07:56,675 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 17:07:56,728 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 17:09:23,286 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Unknown", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 17:09:23,288 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T17:09:23.287034", "tests_analyzed": ["Malaria"], "interpretation": ["\u2753 **Malaria test result is unknown or not available.** No data was provided for this test.", "**Conclusion:** Unable to determine if you have malaria as no test result was provided.", "\u2753 **Malaria test result is unknown or inconclusive.** The test did not provide a clear positive or negative result.", "**Conclusion:** Unable to determine if you have malaria. Further testing is recommended."], "recommendations": ["\ud83d\udd2c Consider getting a malaria test if you have symptoms like fever, chills, headache, or have traveled to malaria-endemic areas.", "\ud83d\udd2c Consider repeating the malaria test for a definitive result.", "\ud83c\udfe5 If you have symptoms like fever, chills, or headache, consult a healthcare provider regardless of this test result."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 17:09:23,304 - root - INFO - Saved test results data for user d9dac3b7-0662-4ab8-b9bd-272ee36d44ee
2025-05-16 17:12:25,039 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "widal": {"Salmonella Typhi O": "Reactive (1:80)", "Salmonella Typhi H": "Reactive (1:160)", "Salmonella Paratyphi A,H": "Reactive (1:160)", "Salmonella Paratyphi B,H": "Reactive (1:160)"}, "patient_age": 30, "patient_sex": "Male"}
2025-05-16 17:12:25,103 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T17:12:25.103275", "tests_analyzed": ["Widal"], "interpretation": ["\u2753 **Widal test results are unknown or not available.** No data was provided for this test.", "**What is the Widal test?** The Widal test detects antibodies against Salmonella antigens in your blood. It measures your immune response to these bacteria:", "- **Salmonella Typhi O (TO)**: Antibodies against the somatic (O) antigen of Salmonella Typhi, which causes typhoid fever", "- **Salmonella Typhi H (TH)**: Antibodies against the flagellar (H) antigen of Salmonella Typhi", "- **Salmonella Paratyphi A,H**: Antibodies against the flagellar antigen of Salmonella Paratyphi A, which causes paratyphoid fever", "- **Salmonella Paratyphi B,H**: Antibodies against the flagellar antigen of Salmonella Paratyphi B", "**Conclusion:** Unable to determine if you have typhoid or paratyphoid fever as no test results were provided."], "recommendations": ["\ud83d\udd2c Consider getting a Widal test if you have symptoms like persistent fever, headache, abdominal pain, or have been exposed to typhoid."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. No Widal test results were provided. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 17:12:25,109 - root - INFO - Saved test results data for user d9dac3b7-0662-4ab8-b9bd-272ee36d44ee
2025-05-16 17:26:21,725 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Unknown", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 17:26:21,726 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T17:26:21.726367", "tests_analyzed": ["Malaria"], "interpretation": ["\u2753 **Malaria test result is unknown or not available.** No data was provided for this test.", "**Conclusion:** Unable to determine if you have malaria as no test result was provided.", "\u2753 **Malaria test result is unknown or inconclusive.** The test did not provide a clear positive or negative result.", "**Conclusion:** Unable to determine if you have malaria. Further testing is recommended."], "recommendations": ["\ud83d\udd2c Consider getting a malaria test if you have symptoms like fever, chills, headache, or have traveled to malaria-endemic areas.", "\ud83d\udd2c Consider repeating the malaria test for a definitive result.", "\ud83c\udfe5 If you have symptoms like fever, chills, or headache, consult a healthcare provider regardless of this test result."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 17:26:21,742 - root - INFO - Saved test results data for user 3663c94a-afd3-4f91-baae-0ebff3ecbe0f
2025-05-16 17:34:58,294 - root - INFO - Successfully imported health tools
2025-05-16 17:34:58,299 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 17:34:58,299 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 17:34:58,329 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 17:34:58,330 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 17:34:58,331 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 17:34:58,365 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 17:42:58,406 - root - INFO - Successfully imported health tools
2025-05-16 17:42:58,407 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 17:42:58,408 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 17:42:58,439 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 17:42:58,439 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 17:42:58,439 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 17:42:58,471 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 17:43:43,199 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Unknown", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 17:43:43,200 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T17:43:43.200640", "tests_analyzed": ["Malaria"], "interpretation": ["\u2753 **Malaria test result is unknown or not available.** No data was provided for this test.", "**Conclusion:** Unable to determine if you have malaria as no test result was provided.", "\u2753 **Malaria test result is unknown or inconclusive.** The test did not provide a clear positive or negative result.", "**Conclusion:** Unable to determine if you have malaria. Further testing is recommended."], "recommendations": ["\ud83d\udd2c Consider getting a malaria test if you have symptoms like fever, chills, headache, or have traveled to malaria-endemic areas.", "\ud83d\udd2c Consider repeating the malaria test for a definitive result.", "\ud83c\udfe5 If you have symptoms like fever, chills, or headache, consult a healthcare provider regardless of this test result."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 17:43:43,203 - root - INFO - Saved test results data for user e0fa7d0f-d595-4414-abf3-3b687ea89c34
2025-05-16 17:46:17,100 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "patient_age": 30, "patient_sex": "Male"}
2025-05-16 17:46:17,100 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T17:46:17.100252", "tests_analyzed": [], "interpretation": [], "recommendations": [], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 17:46:17,104 - root - INFO - Saved test results data for user e0fa7d0f-d595-4414-abf3-3b687ea89c34
2025-05-16 17:51:59,907 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Unknown", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 17:51:59,908 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T17:51:59.908025", "tests_analyzed": ["Malaria"], "interpretation": ["\u2753 **Malaria test result is unknown or not available.** No data was provided for this test.", "**Conclusion:** Unable to determine if you have malaria as no test result was provided.", "\u2753 **Malaria test result is unknown or inconclusive.** The test did not provide a clear positive or negative result.", "**Conclusion:** Unable to determine if you have malaria. Further testing is recommended."], "recommendations": ["\ud83d\udd2c Consider getting a malaria test if you have symptoms like fever, chills, headache, or have traveled to malaria-endemic areas.", "\ud83d\udd2c Consider repeating the malaria test for a definitive result.", "\ud83c\udfe5 If you have symptoms like fever, chills, or headache, consult a healthcare provider regardless of this test result."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 17:51:59,909 - root - INFO - Saved test results data for user a55a6438-627d-44f4-b172-17d63f833d6e
2025-05-16 17:56:21,460 - root - INFO - Successfully imported health tools
2025-05-16 17:56:21,462 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 17:56:21,462 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 17:56:21,493 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 17:56:21,494 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 17:56:21,494 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 17:56:21,543 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 17:57:22,258 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Unknown", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 17:57:22,264 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T17:57:22.264461", "tests_analyzed": ["Malaria"], "interpretation": ["\u2753 **Malaria test result is unknown or not available.** No data was provided for this test.", "**Conclusion:** Unable to determine if you have malaria as no test result was provided.", "\u2753 **Malaria test result is unknown or inconclusive.** The test did not provide a clear positive or negative result.", "**Conclusion:** Unable to determine if you have malaria. Further testing is recommended."], "recommendations": ["\ud83d\udd2c Consider getting a malaria test if you have symptoms like fever, chills, headache, or have traveled to malaria-endemic areas.", "\ud83d\udd2c Consider repeating the malaria test for a definitive result.", "\ud83c\udfe5 If you have symptoms like fever, chills, or headache, consult a healthcare provider regardless of this test result."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 17:57:22,269 - root - INFO - Saved test results data for user 7b5f69be-895e-416a-92e3-7392e01582aa
2025-05-16 17:58:22,156 - root - INFO - Successfully imported health tools
2025-05-16 17:58:22,158 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 17:58:22,159 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 17:58:22,194 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 17:58:22,195 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 17:58:22,196 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 17:58:22,228 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 17:59:00,351 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Unknown", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 17:59:00,352 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T17:59:00.351040", "tests_analyzed": ["Malaria"], "interpretation": ["\u2753 **Malaria test result is unknown or not available.** No data was provided for this test.", "**Conclusion:** Unable to determine if you have malaria as no test result was provided.", "\u2753 **Malaria test result is unknown or inconclusive.** The test did not provide a clear positive or negative result.", "**Conclusion:** Unable to determine if you have malaria. Further testing is recommended."], "recommendations": ["\ud83d\udd2c Consider getting a malaria test if you have symptoms like fever, chills, headache, or have traveled to malaria-endemic areas.", "\ud83d\udd2c Consider repeating the malaria test for a definitive result.", "\ud83c\udfe5 If you have symptoms like fever, chills, or headache, consult a healthcare provider regardless of this test result."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 17:59:00,356 - root - INFO - Saved test results data for user 734c71dd-4dba-446c-8c4b-12563414faf4
2025-05-16 17:59:29,977 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Negative", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 17:59:29,978 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T17:59:29.978397", "tests_analyzed": ["Malaria"], "interpretation": ["\u2705 **Malaria test is negative.** No malaria parasites were detected in your blood sample.", "**Conclusion:** You do not have malaria based on this test."], "recommendations": ["\ud83d\udee1\ufe0f Continue using preventive measures in malaria-endemic areas (bed nets, insect repellent, etc.).", "\ud83c\udf21\ufe0f If you develop fever or other symptoms, consider retesting as early infections may not be detected."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. \u2705 MALARIA STATUS: NEGATIVE - Your malaria test is negative. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 17:59:29,984 - root - INFO - Saved test results data for user 734c71dd-4dba-446c-8c4b-12563414faf4
2025-05-16 18:00:15,801 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Negative", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 18:00:15,806 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T18:00:15.806688", "tests_analyzed": ["Malaria"], "interpretation": ["\u2705 **Malaria test is negative.** No malaria parasites were detected in your blood sample.", "**Conclusion:** You do not have malaria based on this test."], "recommendations": ["\ud83d\udee1\ufe0f Continue using preventive measures in malaria-endemic areas (bed nets, insect repellent, etc.).", "\ud83c\udf21\ufe0f If you develop fever or other symptoms, consider retesting as early infections may not be detected."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. \u2705 MALARIA STATUS: NEGATIVE - Your malaria test is negative. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 18:00:15,810 - root - INFO - Saved test results data for user 734c71dd-4dba-446c-8c4b-12563414faf4
2025-05-16 18:00:44,240 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "malaria": "Positive", "patient_age": 30, "patient_sex": "Male"}
2025-05-16 18:00:44,245 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T18:00:44.245142", "tests_analyzed": ["Malaria"], "interpretation": ["\u26a0\ufe0f **Malaria detected.** This requires prompt medical treatment.", "**Conclusion:** You have malaria (species unidentified)."], "recommendations": ["\ud83c\udfe5 See a doctor within 24 hours for appropriate antimalarial medication.", "\ud83c\udf21\ufe0f Monitor for fever, chills, headache, and other symptoms.", "\ud83d\udea8 Due to the urgency of your results, please seek medical attention immediately."], "urgency_level": "High", "flagged_results": ["Malaria (unspecified)"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some concerning results that require prompt medical attention. Specifically, I'm concerned about: Malaria (unspecified). \ud83d\udea8 MALARIA STATUS: POSITIVE - You have malaria, which requires prompt treatment with appropriate antimalarial medication. With proper treatment, most people recover completely. "}
2025-05-16 18:00:44,267 - root - INFO - Saved test results data for user 734c71dd-4dba-446c-8c4b-12563414faf4
2025-05-16 18:01:44,272 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-16 18:01:44,281 - root - INFO - Retrieved context: ...
2025-05-16 18:01:44,281 - root - INFO - Added health data context to system prompt for user 734c71dd-4dba-446c-8c4b-12563414faf4
2025-05-16 18:01:59,107 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-16 18:03:42,971 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "widal": {"Salmonella Typhi O": "Reactive (1:80)", "Salmonella Typhi H": "Non-Reactive", "Salmonella Paratyphi A,H": "Reactive (1:160)", "Salmonella Paratyphi B,H": "Non-Reactive"}, "patient_age": 30, "patient_sex": "Male"}
2025-05-16 18:03:42,972 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T18:03:42.972523", "tests_analyzed": ["Widal"], "interpretation": ["\u2753 **Widal test results are unknown or not available.** No data was provided for this test.", "**What is the Widal test?** The Widal test detects antibodies against Salmonella antigens in your blood. It measures your immune response to these bacteria:", "- **Salmonella Typhi O (TO)**: Antibodies against the somatic (O) antigen of Salmonella Typhi, which causes typhoid fever", "- **Salmonella Typhi H (TH)**: Antibodies against the flagellar (H) antigen of Salmonella Typhi", "- **Salmonella Paratyphi A,H**: Antibodies against the flagellar antigen of Salmonella Paratyphi A, which causes paratyphoid fever", "- **Salmonella Paratyphi B,H**: Antibodies against the flagellar antigen of Salmonella Paratyphi B", "**Conclusion:** Unable to determine if you have typhoid or paratyphoid fever as no test results were provided."], "recommendations": ["\ud83d\udd2c Consider getting a Widal test if you have symptoms like persistent fever, headache, abdominal pain, or have been exposed to typhoid."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. \u2753 WIDAL STATUS: UNKNOWN - No Widal test results were provided. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 18:03:42,974 - root - INFO - Saved test results data for user 734c71dd-4dba-446c-8c4b-12563414faf4
2025-05-16 18:04:54,706 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "widal": {"Salmonella Typhi O": "Reactive (1:80)", "Salmonella Typhi H": "Reactive (1:160)", "Salmonella Paratyphi A,H": "Reactive (1:160)", "Salmonella Paratyphi B,H": "Reactive (1:160)"}, "patient_age": 30, "patient_sex": "Male"}
2025-05-16 18:04:54,707 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T18:04:54.707549", "tests_analyzed": ["Widal"], "interpretation": ["\u2753 **Widal test results are unknown or not available.** No data was provided for this test.", "**What is the Widal test?** The Widal test detects antibodies against Salmonella antigens in your blood. It measures your immune response to these bacteria:", "- **Salmonella Typhi O (TO)**: Antibodies against the somatic (O) antigen of Salmonella Typhi, which causes typhoid fever", "- **Salmonella Typhi H (TH)**: Antibodies against the flagellar (H) antigen of Salmonella Typhi", "- **Salmonella Paratyphi A,H**: Antibodies against the flagellar antigen of Salmonella Paratyphi A, which causes paratyphoid fever", "- **Salmonella Paratyphi B,H**: Antibodies against the flagellar antigen of Salmonella Paratyphi B", "**Conclusion:** Unable to determine if you have typhoid or paratyphoid fever as no test results were provided."], "recommendations": ["\ud83d\udd2c Consider getting a Widal test if you have symptoms like persistent fever, headache, abdominal pain, or have been exposed to typhoid."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. \u2753 WIDAL STATUS: UNKNOWN - No Widal test results were provided. All the parameters I checked are within expected ranges. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 18:04:54,721 - root - INFO - Saved test results data for user 734c71dd-4dba-446c-8c4b-12563414faf4
2025-05-16 18:06:51,101 - root - INFO - Saved lung capacity data for user 734c71dd-4dba-446c-8c4b-12563414faf4
2025-05-16 18:12:47,183 - root - INFO - Successfully imported health tools
2025-05-16 18:12:47,187 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 18:12:47,187 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 18:12:47,233 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 18:12:47,234 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 18:12:47,235 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 18:12:47,289 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 18:13:49,179 - root - INFO - Saved lung capacity data for user 96be51b4-fa60-4da4-a116-32fbcc7faa3e
2025-05-16 18:17:26,570 - root - INFO - Successfully imported health tools
2025-05-16 18:17:26,573 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 18:17:26,574 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 18:17:26,612 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 18:17:26,614 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 18:17:26,614 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 18:17:26,645 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 18:18:36,546 - root - INFO - Processing lung capacity data: {"FEV1": 85.0, "FVC": 53.0, "FEV1_FVC_ratio": 0.6, "PEF": 66.5, "FEF25_75": 75.0, "Age": 40, "Sex": "Male", "Height": 170.0, "Race": "African American", "Smoking_Status": "Non-smoker"}
2025-05-16 18:18:36,547 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 85.0% of predicted value (2.49L, expected 2.93L)", "FVC is 53.0% of predicted value (1.91L, expected 3.60L)", "PEF is 66.5% of predicted value (369.31L/min, expected 555.36L/min)", "Based on your age, height, and demographics, your expected values are: FEV1 2.93L, FVC 3.60L, PEF 555.36L/min", "FEV1 is 85.0% of predicted value, which is within normal range.", "FVC is 53.0% of predicted value, indicating severe restrictive pattern.", "FEV1/FVC ratio is 0.6, indicating obstructive lung disease.", "Pattern may be consistent with asthma.", "PEF is 66.5% of predicted value, indicating reduced peak flow.", "FEF25-75 is 75.0% of predicted value, which is within normal range."], "respiratory_risk_level": "High", "potential_conditions": ["Obstructive lung disease", "Possible asthma"], "recommendations": ["\ud83e\udec1 Consider pulmonary function testing with bronchodilator response to confirm asthma diagnosis.", "\ud83e\uddea Allergy testing may help identify triggers.", "\ud83d\udc68\u200d\u2695\ufe0f Consult with a pulmonologist or allergist for asthma management.", "\ud83d\udca8 Practice breathing exercises to improve lung function.", "\ud83c\udfc3 Regular aerobic exercise can help improve respiratory capacity.", "\ud83d\ude37 Avoid respiratory irritants and air pollution when possible."], "confidence_level": "High", "missing_parameters": [], "doctor_summary": "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: I'm seeing some significant concerns with your lung function that we should address. The test provided comprehensive data, so I'm quite confident in this assessment. Based on these patterns, I'm seeing indicators that could be associated with Obstructive lung disease, Possible asthma. Remember, these are preliminary findings based on your spirometry results, not a definitive diagnosis."}
2025-05-16 18:18:36,550 - root - INFO - Saved lung capacity data for user 2c3fa731-3c01-4833-abe7-4fcdeadeccee
2025-05-16 18:33:37,082 - root - INFO - Successfully imported health tools
2025-05-16 18:33:37,103 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 18:33:37,167 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 18:33:37,564 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 18:33:37,601 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 18:33:37,657 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 18:33:38,122 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 18:34:16,116 - root - INFO - Processing lung capacity data: {"FEV1": 85.0, "FVC": 90.0, "FEV1_FVC_ratio": 0.7, "PEF": 85.0, "FEF25_75": 75.0, "Age": 40, "Sex": "Male", "Height": 170.0, "Race": "Caucasian", "Smoking_Status": "Non-smoker"}
2025-05-16 18:34:16,133 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 85.0% of predicted value (2.86L, expected 3.36L)", "FVC is 90.0% of predicted value (3.73L, expected 4.14L)", "PEF is 85.0% of predicted value (472.06L/min, expected 555.36L/min)", "Based on your age, height, and demographics, your expected values are: FEV1 3.36L, FVC 4.14L, PEF 555.36L/min", "FEV1 is 85.0% of predicted value, which is within normal range.", "FVC is 90.0% of predicted value, which is within normal range.", "FEV1/FVC ratio is 0.7, which is within normal range.", "PEF is 85.0% of predicted value, which is within normal range.", "FEF25-75 is 75.0% of predicted value, which is within normal range."], "respiratory_risk_level": "Low", "potential_conditions": ["No specific respiratory conditions identified"], "recommendations": ["\u2705 Continue regular health maintenance and preventive care."], "confidence_level": "High", "missing_parameters": [], "doctor_summary": "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: Your lung function appears to be in good shape overall. The test provided comprehensive data, so I'm quite confident in this assessment."}
2025-05-16 18:34:16,222 - root - INFO - Saved lung capacity data for user 07d58221-1608-4d4c-b748-f65a700a3930
2025-05-16 18:48:51,375 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-16 18:48:51,384 - root - INFO - Retrieved context: ...
2025-05-16 18:48:51,385 - root - INFO - Added health data context to system prompt for user 07d58221-1608-4d4c-b748-f65a700a3930
2025-05-16 18:49:02,608 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-16 18:49:27,553 - root - INFO - Processing lung capacity data: {"FEV1": 85.0, "FVC": 90.0, "FEV1_FVC_ratio": 0.7, "PEF": 85.0, "FEF25_75": 75.0, "Age": 40, "Sex": "Male", "Height": 170.0, "Race": "Caucasian", "Smoking_Status": "Non-smoker"}
2025-05-16 18:49:27,569 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 85.0% of predicted value (2.86L, expected 3.36L)", "FVC is 90.0% of predicted value (3.73L, expected 4.14L)", "PEF is 85.0% of predicted value (472.06L/min, expected 555.36L/min)", "Based on your age, height, and demographics, your expected values are: FEV1 3.36L, FVC 4.14L, PEF 555.36L/min", "FEV1 is 85.0% of predicted value, which is within normal range.", "FVC is 90.0% of predicted value, which is within normal range.", "FEV1/FVC ratio is 0.7, which is within normal range.", "PEF is 85.0% of predicted value, which is within normal range.", "FEF25-75 is 75.0% of predicted value, which is within normal range."], "respiratory_risk_level": "Low", "potential_conditions": ["No specific respiratory conditions identified"], "recommendations": ["\u2705 Continue regular health maintenance and preventive care."], "confidence_level": "High", "missing_parameters": [], "doctor_summary": "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: Your lung function appears to be in good shape overall. The test provided comprehensive data, so I'm quite confident in this assessment."}
2025-05-16 18:49:27,583 - root - INFO - Saved lung capacity data for user 6b01d682-e852-49d0-96cd-02544d970ef6
2025-05-16 18:57:24,448 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "patient_age": 40, "patient_sex": "Male", "malaria": "Negative"}
2025-05-16 18:57:24,451 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T18:57:24.451510", "tests_analyzed": ["Malaria Test"], "interpretation": ["Your malaria test is negative, suggesting no detectable malaria parasites in your blood at this time.", "Note that early infections may not always be detected. If symptoms persist, consider retesting in 24-48 hours."], "recommendations": ["\ud83e\udd9f Continue using preventive measures like bed nets and insect repellent in malaria-endemic areas."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture."}
2025-05-16 18:57:24,461 - root - INFO - Saved test results data for user bee6be2a-b546-49ab-be4b-5624f39b4842
2025-05-16 18:58:26,957 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "patient_age": 40, "patient_sex": "Male", "malaria": "Positive"}
2025-05-16 18:58:26,970 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T18:58:26.970098", "tests_analyzed": ["Malaria Test"], "interpretation": ["Your malaria test is positive, indicating the presence of malaria parasites in your blood.", "Malaria requires prompt treatment to prevent complications."], "recommendations": ["\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider within 24 hours for appropriate antimalarial medication.", "\ud83e\udd9f Use bed nets and insect repellent to prevent mosquito bites.", "\ud83d\udca7 Eliminate standing water around your home to reduce mosquito breeding sites.", "\ud83c\udf21\ufe0f Monitor your temperature regularly and stay well-hydrated."], "urgency_level": "Medium", "flagged_results": ["Malaria Test"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some results that should be discussed with a healthcare provider. Let me explain what these results mean and what steps you should consider taking."}
2025-05-16 18:58:26,971 - root - INFO - Saved test results data for user bee6be2a-b546-49ab-be4b-5624f39b4842
2025-05-16 19:01:42,606 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-16 19:02:36,078 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-16 19:02:36,158 - root - INFO - Original health data: {"Glucose": 100.0, "SpO2": 85.0, "ECG (Heart Rate)": 160.0, "Blood Pressure (Systolic)": 128.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-16 19:02:36,236 - root - INFO - Health data received: {"Glucose": 100.0, "SpO2": 85.0, "ECG (Heart Rate)": 160.0, "Blood Pressure (Systolic)": 128.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-16 19:02:36,316 - root - INFO - Processed health data: {"Glucose": 100.0, "SpO2": 85.0, "ECG (Heart Rate)": 160.0, "Blood Pressure (Systolic)": 128.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Unknown", "Widal Test": "Positive", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-16 19:02:36,322 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-16 19:02:36,325 - root - INFO - Health score report generated: {"Total Score": 47, "Health Status": "Poor", "Vitals Needing Improvement": ["SpO2 (Low)", "ECG (Heart Rate) (Abnormal)", "Blood Pressure (Systolic) (Abnormal)", "Widal Test (Positive)"], "Improvement Tips": ["\ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.", "\u2764\ufe0f For your elevated heart rate, try practicing mindfulness meditation, limiting caffeine, and ensuring adequate rest. If it remains consistently high, consult your doctor.", "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83e\ude7a Your test results for Widal Test require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-16 19:02:36,334 - root - INFO - Saved health score data for user bee6be2a-b546-49ab-be4b-5624f39b4842
2025-05-16 19:08:44,438 - root - INFO - Successfully imported health tools
2025-05-16 19:08:44,441 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-16 19:08:44,442 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-16 19:08:44,491 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-16 19:08:44,493 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-16 19:08:44,494 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-16 19:08:44,577 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-16 19:14:23,402 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "patient_age": 40, "patient_sex": "Male", "widal": {"Typhi O": "Non-Reactive", "Typhi H": "Non-Reactive", "Paratyphi A,H": "Non-Reactive", "Paratyphi B,H": "Non-Reactive"}}
2025-05-16 19:14:23,408 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T19:14:23.408851", "tests_analyzed": ["Widal Test (Typhoid)"], "interpretation": ["Your Widal test shows no reactivity to Salmonella antigens, suggesting no evidence of typhoid or paratyphoid fever."], "recommendations": ["\ud83e\uddfc Continue practicing good hygiene and safe food handling to prevent enteric infections.", "\ud83c\udf7d\ufe0f When traveling to areas where typhoid is common, remember to 'boil it, cook it, peel it, or forget it' to prevent foodborne illness."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-16 19:14:23,441 - root - INFO - Saved test results data for user 82068f7c-ddc9-444b-99d2-6422a70e71a0
2025-05-16 19:15:46,980 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "patient_age": 40, "patient_sex": "Male", "widal": {"Typhi O": "Non-Reactive", "Typhi H": "Non-Reactive", "Paratyphi A,H": "Non-Reactive", "Paratyphi B,H": "Non-Reactive"}}
2025-05-16 19:15:46,993 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T19:15:46.993036", "tests_analyzed": ["Widal Test (Typhoid)"], "interpretation": ["Your Widal test shows no reactivity to Salmonella antigens, suggesting no evidence of typhoid or paratyphoid fever."], "recommendations": ["\ud83e\uddfc Continue practicing good hygiene and safe food handling to prevent enteric infections.", "\ud83c\udf7d\ufe0f When traveling to areas where typhoid is common, remember to 'boil it, cook it, peel it, or forget it' to prevent foodborne illness."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-16 19:15:46,994 - root - INFO - Saved test results data for user 82068f7c-ddc9-444b-99d2-6422a70e71a0
2025-05-16 19:16:09,905 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "patient_age": 40, "patient_sex": "Male", "widal": {"Typhi O": "Non-Reactive", "Typhi H": "Non-Reactive", "Paratyphi A,H": "Non-Reactive", "Paratyphi B,H": "Non-Reactive"}}
2025-05-16 19:16:09,909 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T19:16:09.909617", "tests_analyzed": ["Widal Test (Typhoid)"], "interpretation": ["Your Widal test shows no reactivity to Salmonella antigens, suggesting no evidence of typhoid or paratyphoid fever."], "recommendations": ["\ud83e\uddfc Continue practicing good hygiene and safe food handling to prevent enteric infections.", "\ud83c\udf7d\ufe0f When traveling to areas where typhoid is common, remember to 'boil it, cook it, peel it, or forget it' to prevent foodborne illness."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-16 19:16:09,925 - root - INFO - Saved test results data for user 82068f7c-ddc9-444b-99d2-6422a70e71a0
2025-05-16 19:17:55,154 - root - INFO - Processing lung capacity data: {"FEV1": 69.0, "FVC": 53.0, "FEV1_FVC_ratio": 0.5, "PEF": 108.5, "FEF25_75": 43.5, "Age": 40, "Sex": "Male", "Height": 124.0, "Race": "Asian", "Smoking_Status": "Current smoker"}
2025-05-16 19:17:55,155 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 69.0% of predicted value (0.90L, expected 1.30L)", "FVC is 53.0% of predicted value (0.89L, expected 1.67L)", "PEF is 108.5% of predicted value (438.46L/min, expected 404.11L/min)", "Based on your age, height, and demographics, your expected values are: FEV1 1.30L, FVC 1.67L, PEF 404.11L/min", "FEV1 is 69.0% of predicted value, indicating moderate airflow limitation.", "FVC is 53.0% of predicted value, indicating severe restrictive pattern.", "FEV1/FVC ratio is 0.5, indicating obstructive lung disease.", "PEF is 108.5% of predicted value, which is within normal range.", "FEF25-75 is 43.5% of predicted value, indicating small airway dysfunction."], "respiratory_risk_level": "High", "potential_conditions": ["Obstructive lung disease", "Possible COPD"], "recommendations": ["\ud83e\udec1 Consider pulmonary function testing with bronchodilator response to confirm COPD diagnosis.", "\ud83d\udead Smoking cessation is essential to prevent further lung damage.", "\ud83d\udc68\u200d\u2695\ufe0f Consult with a pulmonologist for comprehensive COPD management.", "\ud83d\udca8 Practice breathing exercises to improve lung function.", "\ud83c\udfc3 Regular aerobic exercise can help improve respiratory capacity.", "\ud83d\ude37 Avoid respiratory irritants and air pollution when possible."], "confidence_level": "High", "missing_parameters": [], "doctor_summary": "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: I'm seeing some significant concerns with your lung function that we should address. The test provided comprehensive data, so I'm quite confident in this assessment. Based on these patterns, I'm seeing indicators that could be associated with Obstructive lung disease, Possible COPD. Remember, these are preliminary findings based on your spirometry results, not a definitive diagnosis."}
2025-05-16 19:17:55,161 - root - INFO - Saved lung capacity data for user e99ddb8f-26fc-4d97-9785-2135f0b59c96
2025-05-16 19:20:19,375 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-16 19:20:19,381 - root - INFO - Retrieved context: ...
2025-05-16 19:20:19,381 - root - INFO - Added health data context to system prompt for user e99ddb8f-26fc-4d97-9785-2135f0b59c96
2025-05-16 19:20:56,762 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-16 19:20:56,763 - root - INFO - Detected primary intent: lung_capacity with score 1.5
2025-05-16 20:09:54,390 - root - INFO - Processing lung capacity data: {"FEV1": 64.0, "FVC": 70.0, "FEV1_FVC_ratio": 0.6, "PEF": 62.5, "FEF25_75": 67.0, "Age": 40, "Sex": "Male", "Height": 170.0, "Race": "African American", "Smoking_Status": "Former smoker"}
2025-05-16 20:09:54,395 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 64.0% of predicted value (1.87L, expected 2.93L)", "FVC is 70.0% of predicted value (2.52L, expected 3.60L)", "PEF is 62.5% of predicted value (347.10L/min, expected 555.36L/min)", "Based on your age, height, and demographics, your expected values are: FEV1 2.93L, FVC 3.60L, PEF 555.36L/min", "FEV1 is 64.0% of predicted value, indicating moderate airflow limitation.", "FVC is 70.0% of predicted value, indicating mild restrictive pattern.", "FEV1/FVC ratio is 0.6, indicating obstructive lung disease.", "PEF is 62.5% of predicted value, indicating reduced peak flow.", "FEF25-75 is 67.0% of predicted value, which is within normal range."], "respiratory_risk_level": "High", "potential_conditions": ["Obstructive lung disease", "Possible COPD"], "recommendations": ["\ud83e\udec1 Consider pulmonary function testing with bronchodilator response to confirm COPD diagnosis.", "\ud83d\udead Smoking cessation is essential to prevent further lung damage.", "\ud83d\udc68\u200d\u2695\ufe0f Consult with a pulmonologist for comprehensive COPD management.", "\ud83d\udca8 Practice breathing exercises to improve lung function.", "\ud83c\udfc3 Regular aerobic exercise can help improve respiratory capacity.", "\ud83d\ude37 Avoid respiratory irritants and air pollution when possible."], "confidence_level": "High", "missing_parameters": [], "doctor_summary": "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: I'm seeing some significant concerns with your lung function that we should address. The test provided comprehensive data, so I'm quite confident in this assessment. Based on these patterns, I'm seeing indicators that could be associated with Obstructive lung disease, Possible COPD. Remember, these are preliminary findings based on your spirometry results, not a definitive diagnosis."}
2025-05-16 20:09:54,400 - root - INFO - Saved lung capacity data for user 1c7011df-9fa3-4369-a90a-df325f22d90f
2025-05-16 20:12:10,683 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-16 20:12:10,686 - root - INFO - Retrieved context: ...
2025-05-16 20:12:10,687 - root - INFO - Added health data context to system prompt for user 1c7011df-9fa3-4369-a90a-df325f22d90f
2025-05-16 20:12:52,588 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-16 20:12:52,589 - root - INFO - Detected primary intent: lung_capacity with score 1.0
2025-05-16 20:14:38,695 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "patient_age": 40, "patient_sex": "Male", "malaria": "Positive"}
2025-05-16 20:14:38,696 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T20:14:38.695426", "tests_analyzed": ["Malaria Test"], "interpretation": ["Your malaria test is positive, indicating the presence of malaria parasites in your blood.", "Malaria requires prompt treatment to prevent complications."], "recommendations": ["\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider within 24 hours for appropriate antimalarial medication.", "\ud83e\udd9f Use bed nets and insect repellent to prevent mosquito bites.", "\ud83d\udca7 Eliminate standing water around your home to reduce mosquito breeding sites.", "\ud83c\udf21\ufe0f Monitor your temperature regularly and stay well-hydrated."], "urgency_level": "Medium", "flagged_results": ["Malaria Test"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some results that should be discussed with a healthcare provider. Let me explain what these results mean and what steps you should consider taking.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-16 20:14:38,697 - root - INFO - Saved test results data for user 1c7011df-9fa3-4369-a90a-df325f22d90f
2025-05-16 20:15:49,312 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "patient_age": 40, "patient_sex": "Male", "malaria": "Negative"}
2025-05-16 20:15:49,312 - root - INFO - Test results interpretation: {"timestamp": "2025-05-16T20:15:49.312476", "tests_analyzed": ["Malaria Test"], "interpretation": ["Your malaria test is negative, suggesting no detectable malaria parasites in your blood at this time.", "Note that early infections may not always be detected. If symptoms persist, consider retesting in 24-48 hours."], "recommendations": ["\ud83e\udd9f Continue using preventive measures like bed nets and insect repellent in malaria-endemic areas."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-16 20:15:49,313 - root - INFO - Saved test results data for user 1c7011df-9fa3-4369-a90a-df325f22d90f
2025-05-16 20:18:35,811 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-16 20:19:20,209 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-16 20:19:20,315 - root - INFO - Original health data: {"Glucose": 129.0, "SpO2": null, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 104.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 18.9, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Negative", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-16 20:19:20,319 - root - INFO - Health data received: {"Glucose": 129.0, "SpO2": null, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 104.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 18.9, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Negative", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-16 20:19:20,331 - root - INFO - Processed health data: {"Glucose": 129.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 104.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 18.9, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Negative", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-16 20:19:20,335 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-16 20:19:20,338 - root - INFO - Health score report generated: {"Total Score": 69, "Health Status": "Fair", "Vitals Needing Improvement": ["Glucose (Abnormal)", "Malaria (Positive)"], "Improvement Tips": ["\ud83e\ude7a Your glucose levels warrant a discussion with your healthcare provider who can recommend appropriate monitoring and management strategies.", "\ud83e\ude7a Your test results for Malaria require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-16 20:19:20,343 - root - INFO - Saved health score data for user 1c7011df-9fa3-4369-a90a-df325f22d90f
2025-05-17 11:35:22,037 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "patient_age": 40, "patient_sex": "Male", "malaria": "Negative"}
2025-05-17 11:35:22,040 - root - INFO - Test results interpretation: {"timestamp": "2025-05-17T11:35:22.037625", "tests_analyzed": ["Malaria Test"], "interpretation": ["Your malaria test is negative, suggesting no detectable malaria parasites in your blood at this time.", "Note that early infections may not always be detected. If symptoms persist, consider retesting in 24-48 hours."], "recommendations": ["\ud83e\udd9f Continue using preventive measures like bed nets and insect repellent in malaria-endemic areas."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-17 11:35:22,042 - root - INFO - Saved test results data for user f523bf21-451d-409b-82dc-6e90c9f8d327
2025-05-17 11:36:02,156 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "patient_age": 40, "patient_sex": "Male", "widal": {"Typhi O": "Non-Reactive", "Typhi H": "Non-Reactive", "Paratyphi A,H": "Non-Reactive", "Paratyphi B,H": "Non-Reactive"}}
2025-05-17 11:36:02,158 - root - INFO - Test results interpretation: {"timestamp": "2025-05-17T11:36:02.158192", "tests_analyzed": ["Widal Test (Typhoid)"], "interpretation": ["Your Widal test shows no reactivity to Salmonella antigens, suggesting no evidence of typhoid or paratyphoid fever."], "recommendations": ["\ud83e\uddfc Continue practicing good hygiene and safe food handling to prevent enteric infections.", "\ud83c\udf7d\ufe0f When traveling to areas where typhoid is common, remember to 'boil it, cook it, peel it, or forget it' to prevent foodborne illness."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-17 11:36:02,158 - root - INFO - Saved test results data for user f523bf21-451d-409b-82dc-6e90c9f8d327
2025-05-17 11:36:50,940 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "patient_age": 40, "patient_sex": "Male", "widal": {"Typhi O": "Reactive", "Typhi H": "Non-Reactive", "Paratyphi A,H": "Reactive", "Paratyphi B,H": "Non-Reactive"}}
2025-05-17 11:36:50,940 - root - INFO - Test results interpretation: {"timestamp": "2025-05-17T11:36:50.940580", "tests_analyzed": ["Widal Test (Typhoid)"], "interpretation": ["Your Widal test shows reactivity to Typhi O, Paratyphi A,H.", "Reactivity to Typhi O antigen alone may indicate early typhoid infection or past exposure to Salmonella Typhi bacteria.", "Reactivity to Paratyphi A,H suggests possible paratyphoid fever, caused by Salmonella Paratyphi. This illness is similar to typhoid but often presents with milder symptoms."], "recommendations": ["\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider for clinical correlation and possible follow-up testing such as blood culture or PCR.", "\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider for evaluation and appropriate antibiotic treatment.", "\ud83e\uddfc Practice strict hand hygiene, especially before eating and after using the toilet.", "\ud83e\udd64 Drink only purified or boiled water and avoid raw foods that may be contaminated.", "\ud83c\udf7d\ufe0f Avoid eating food from street vendors or restaurants with questionable hygiene practices.", "\ud83d\udeab Avoid preparing food for others until cleared by a healthcare provider to prevent spreading the infection."], "urgency_level": "Low", "flagged_results": ["Widal Test (Typhoid)"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some results that should be discussed with a healthcare provider. Let me explain what these results mean and what steps you should consider taking.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-17 11:36:50,993 - root - INFO - Saved test results data for user f523bf21-451d-409b-82dc-6e90c9f8d327
2025-05-17 11:39:04,804 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "patient_age": 40, "patient_sex": "Male", "widal": {"Typhi O": "Non-Reactive", "Typhi H": "Reactive", "Paratyphi A,H": "Non-Reactive", "Paratyphi B,H": "Reactive"}}
2025-05-17 11:39:04,804 - root - INFO - Test results interpretation: {"timestamp": "2025-05-17T11:39:04.804491", "tests_analyzed": ["Widal Test (Typhoid)"], "interpretation": ["Your Widal test shows reactivity to Typhi H, Paratyphi B,H.", "Reactivity to Typhi H antigen alone may indicate past typhoid infection, carrier state, or cross-reactivity with other Salmonella species.", "Reactivity to Paratyphi B,H suggests possible paratyphoid fever, caused by Salmonella Paratyphi. This illness is similar to typhoid but often presents with milder symptoms."], "recommendations": ["\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider for clinical correlation and possible follow-up testing such as blood culture or PCR.", "\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider for evaluation and appropriate antibiotic treatment.", "\ud83e\uddfc Practice strict hand hygiene, especially before eating and after using the toilet.", "\ud83e\udd64 Drink only purified or boiled water and avoid raw foods that may be contaminated.", "\ud83c\udf7d\ufe0f Avoid eating food from street vendors or restaurants with questionable hygiene practices.", "\ud83d\udeab Avoid preparing food for others until cleared by a healthcare provider to prevent spreading the infection."], "urgency_level": "Low", "flagged_results": ["Widal Test (Typhoid)"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some results that should be discussed with a healthcare provider. Let me explain what these results mean and what steps you should consider taking.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-17 11:39:04,811 - root - INFO - Saved test results data for user 11b88b9e-0e71-41ab-a3ba-ed363ed5bc74
2025-05-17 12:02:38,204 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-17 12:03:14,177 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-17 12:03:14,226 - root - INFO - Original health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 131.0, "Blood Pressure (Diastolic)": 108.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Negative", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-17 12:03:14,230 - root - INFO - Health data received: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 131.0, "Blood Pressure (Diastolic)": 108.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Negative", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-17 12:03:14,265 - root - INFO - Processed health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 131.0, "Blood Pressure (Diastolic)": 108.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Negative", "Voluntary Serology": "Unknown", "Perfusion_index": 5.0, "Waist Circumference": 80.0, "Fev": 85.0}
2025-05-17 12:03:14,265 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-17 12:03:14,272 - root - INFO - Health score report generated: {"Total Score": 72, "Health Status": "Good", "Vitals Needing Improvement": ["Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Malaria (Positive)"], "Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83e\ude7a Your test results for Malaria require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-17 12:03:14,272 - root - INFO - Saved health score data for user 11b88b9e-0e71-41ab-a3ba-ed363ed5bc74
2025-05-17 12:20:37,337 - root - INFO - Successfully imported health tools
2025-05-17 12:20:37,349 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-17 12:20:37,355 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-17 12:20:37,387 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-17 12:20:37,400 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-17 12:20:37,408 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-17 12:20:37,441 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-17 12:21:18,568 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-17 12:21:56,102 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-17 12:21:56,156 - root - INFO - Original health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": 20.0, "Waist Circumference": 53.0, "Fev": 23.0}
2025-05-17 12:21:56,168 - root - INFO - Health data received: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": 20.0, "Waist Circumference": 53.0, "Fev": 23.0}
2025-05-17 12:21:56,196 - root - INFO - Processed health data: {"Glucose": 100.0, "SpO2": 98.0, "ECG (Heart Rate)": 75.0, "Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "Weight (BMI)": 24.2, "Temperature": 36.8, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": 20.0, "Waist Circumference": 53.0, "Fev": 23.0}
2025-05-17 12:21:56,198 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-17 12:21:56,203 - root - INFO - Health score report generated: {"Total Score": 72, "Health Status": "Good", "Vitals Needing Improvement": ["Malaria (Positive)", "Widal Test (Positive)", "Fev (Low)"], "Improvement Tips": ["\ud83e\udec1 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor.", "\ud83e\ude7a Your test results for Malaria, Widal Test require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-17 12:21:56,210 - root - INFO - Saved health score data for user 969fc1e0-081a-4b6f-bbe7-f60662dab792
2025-05-17 22:16:48,738 - root - INFO - Successfully imported health tools
2025-05-17 22:16:48,746 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-17 22:16:48,746 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-17 22:16:48,826 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-17 22:16:48,826 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-17 22:16:48,826 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-17 22:16:48,899 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-17 22:23:08,419 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-17 22:23:46,689 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-17 22:23:46,819 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Kidney Function": null, "Lipid Profile": null, "Lung Capacity": null, "Liver Function": null, "Complete Blood Count": null}
2025-05-17 22:23:46,828 - root - INFO - Real-time health score result: {"Total Score": 86, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "timestamp": "2025-05-17T22:23:46.828065", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 43}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": ["Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)", "Voluntary Serology (Positive)"]}, "Detailed Improvement Tips": ["\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-17 22:23:46,828 - root - INFO - Saved real-time health score data for user a6e4125d-492e-4c42-9188-7cd80b713dbc
2025-05-19 10:05:25,981 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 10:07:08,088 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 10:07:08,178 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 136.0, "Blood Pressure (Diastolic)": 108.0, "ECG (Heart Rate)": null, "SpO2": null, "Temperature": 35.5, "Weight (BMI)": 27.5, "Respiratory Rate": null, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 143.0, "Malaria": "Positive", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Negative", "Kidney Function": null, "Lipid Profile": null, "Lung Capacity": null, "Liver Function": null, "Complete Blood Count": null}
2025-05-19 10:07:08,194 - root - INFO - Real-time health score result: {"Total Score": 63, "Health Status": "Fair", "Vitals Needing Improvement": "Blood Pressure (Systolic) (High), Blood Pressure (Diastolic) (High), Temperature (Low), Weight (BMI) (High)", "Improvement Tips": "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity. \ud83d\udcaa Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle. \ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "timestamp": "2025-05-19T10:07:08.194477", "Category Scores": {"Vitals": 0, "Lifestyle": 100, "Test Results": 11}, "Detailed Issues": {"Vitals": ["Blood Pressure (Systolic) (High)", "Blood Pressure (Diastolic) (High)", "Temperature (Low)", "Weight (BMI) (High)"], "Lifestyle": [], "Test Results": ["Glucose (High)", "Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)"]}, "Detailed Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83d\udcaa Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle.", "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-19 10:07:08,219 - root - INFO - Saved real-time health score data for user 5f721bc3-5f3e-4242-9b60-3d061427ed5c
2025-05-19 10:25:48,032 - root - INFO - Saved lipid profile data for user 5f721bc3-5f3e-4242-9b60-3d061427ed5c
2025-05-19 10:27:46,781 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-19 10:27:46,811 - root - INFO - Retrieved context: ...
2025-05-19 10:27:46,811 - root - INFO - Added health data context to system prompt for user 5f721bc3-5f3e-4242-9b60-3d061427ed5c
2025-05-19 10:28:20,656 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-19 10:28:20,657 - root - INFO - Detected primary intent: lipid_profile with score 1.0
2025-05-19 10:29:38,780 - root - INFO - Saved lipid profile data for user 5f721bc3-5f3e-4242-9b60-3d061427ed5c
2025-05-19 14:06:54,886 - root - INFO - Successfully imported health tools
2025-05-19 14:06:54,981 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 14:06:54,993 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 14:06:55,038 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 14:06:55,050 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 14:06:55,052 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 14:06:55,100 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 14:07:37,838 - root - INFO - Saved kidney function data for user 577ebe68-0512-41db-9087-ecd3103ecd31
2025-05-19 15:05:40,323 - root - INFO - Successfully imported health tools
2025-05-19 15:05:40,390 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 15:05:40,390 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 15:05:40,452 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 15:05:40,452 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 15:05:40,452 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 15:05:40,494 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 15:49:56,277 - root - INFO - Processing lung capacity data: {"FEV1": 22.0, "FVC": 52.5, "FEV1_FVC_ratio": 0.5, "PEF": 54.0, "FEF25_75": 63.0, "Age": 40, "Sex": "Female", "Height": 170.0, "Race": "African American", "Smoking_Status": "Former smoker"}
2025-05-19 15:49:56,317 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 22.0% of predicted value (0.64L, expected 2.93L)", "FVC is 52.5% of predicted value (1.89L, expected 3.60L)", "PEF is 54.0% of predicted value (238.59L/min, expected 441.84L/min)", "Based on your age, height, and demographics, your expected values are: FEV1 2.93L, FVC 3.60L, PEF 441.84L/min", "FEV1 is 22.0% of predicted value, indicating very severe airflow limitation.", "FVC is 52.5% of predicted value, indicating severe restrictive pattern.", "FEV1/FVC ratio is 0.5, indicating obstructive lung disease.", "PEF is 54.0% of predicted value, indicating reduced peak flow.", "FEF25-75 is 63.0% of predicted value, which is within normal range."], "respiratory_risk_level": "High", "potential_conditions": ["Obstructive lung disease", "Possible COPD"], "recommendations": ["\ud83e\udec1 Consider pulmonary function testing with bronchodilator response to confirm COPD diagnosis.", "\ud83d\udead Smoking cessation is essential to prevent further lung damage.", "\ud83d\udc68\u200d\u2695\ufe0f Consult with a pulmonologist for comprehensive COPD management.", "\ud83d\udca8 Practice breathing exercises to improve lung function.", "\ud83c\udfc3 Regular aerobic exercise can help improve respiratory capacity.", "\ud83d\ude37 Avoid respiratory irritants and air pollution when possible."], "confidence_level": "High", "missing_parameters": [], "doctor_summary": "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: I'm seeing some significant concerns with your lung function that we should address. The test provided comprehensive data, so I'm quite confident in this assessment. Based on these patterns, I'm seeing indicators that could be associated with Obstructive lung disease, Possible COPD. Remember, these are preliminary findings based on your spirometry results, not a definitive diagnosis."}
2025-05-19 15:49:56,320 - root - INFO - Saved lung capacity data for user 918a2fd9-763e-433c-88e4-ff4bb6eb6205
2025-05-19 15:52:37,604 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 15:54:16,583 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 15:54:16,701 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 133.0, "Blood Pressure (Diastolic)": 104.0, "ECG (Heart Rate)": null, "SpO2": null, "Temperature": 35.0, "Weight (BMI)": 20.1, "Respiratory Rate": 26.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 134.0, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Kidney Function": "Fair", "Lipid Profile": null, "Lung Capacity": null, "Liver Function": null, "Complete Blood Count": null}
2025-05-19 15:54:16,710 - root - INFO - Real-time health score result: {"Total Score": 59, "Health Status": "Fair", "Vitals Needing Improvement": "Blood Pressure (Systolic) (High), Blood Pressure (Diastolic) (High), Temperature (Low), Respiratory Rate (High)", "Improvement Tips": "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity. \ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps. \ud83e\uddea To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations.", "timestamp": "2025-05-19T15:54:16.710032", "Category Scores": {"Vitals": 23, "Lifestyle": 100, "Test Results": 0}, "Detailed Issues": {"Vitals": ["Blood Pressure (Systolic) (High)", "Blood Pressure (Diastolic) (High)", "Temperature (Low)", "Respiratory Rate (High)"], "Lifestyle": [], "Test Results": ["Glucose (High)", "Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)", "Voluntary Serology (Positive)", "Kidney Function (Fair)"]}, "Detailed Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "\ud83e\uddea To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations."]}
2025-05-19 15:54:16,710 - root - INFO - Saved real-time health score data for user 918a2fd9-763e-433c-88e4-ff4bb6eb6205
2025-05-19 15:55:34,952 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 15:55:38,887 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 15:55:38,967 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Kidney Function": null, "Lipid Profile": null, "Lung Capacity": null, "Liver Function": null, "Complete Blood Count": null}
2025-05-19 15:55:38,975 - root - INFO - Real-time health score result: {"Total Score": 86, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "timestamp": "2025-05-19T15:55:38.974576", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 43}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": ["Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)", "Voluntary Serology (Positive)"]}, "Detailed Improvement Tips": ["\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-19 15:55:38,977 - root - INFO - Saved real-time health score data for user 08bdda02-88a2-4182-a1ed-a839fe629cd6
2025-05-19 16:26:15,886 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:26:31,170 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:26:31,281 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Kidney Function": null, "Lipid Profile": null, "Lung Capacity": null, "Liver Function": null, "Complete Blood Count": null}
2025-05-19 16:26:31,289 - root - INFO - Real-time health score result: {"Total Score": 86, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "timestamp": "2025-05-19T16:26:31.289234", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 43}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": ["Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)", "Voluntary Serology (Positive)"]}, "Detailed Improvement Tips": ["\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-19 16:26:31,289 - root - INFO - Saved real-time health score data for user 08bdda02-88a2-4182-a1ed-a839fe629cd6
2025-05-19 16:26:49,444 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:26:56,252 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:26:56,338 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Kidney Function": null, "Lipid Profile": null, "Lung Capacity": null, "Liver Function": null, "Complete Blood Count": null}
2025-05-19 16:26:56,338 - root - INFO - Real-time health score result: {"Total Score": 86, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "timestamp": "2025-05-19T16:26:56.338268", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 43}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": ["Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)", "Voluntary Serology (Positive)"]}, "Detailed Improvement Tips": ["\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-19 16:26:56,338 - root - INFO - Saved real-time health score data for user 9e591b0f-98fa-46dc-aab7-cac494849ddf
2025-05-19 16:38:39,056 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:38:43,838 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:38:43,916 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Kidney Function": "Unknown", "Lipid Profile": "Unknown", "Lung Capacity": "Unknown", "Liver Function": "Unknown", "Complete Blood Count": "Unknown"}
2025-05-19 16:38:43,918 - root - INFO - Real-time health score result: {"Total Score": 78, "Health Status": "Good", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps. \ud83e\uddea To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations. \u2764\ufe0f To improve your cholesterol levels, focus on heart-healthy foods like fatty fish, nuts, olive oil, and fiber-rich fruits and vegetables. \ud83e\udec1 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor.", "timestamp": "2025-05-19T16:38:43.918275", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 12}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": ["Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)", "Voluntary Serology (Positive)", "Kidney Function (Unknown)", "Lipid Profile (Unknown)", "Lung Capacity (Unknown)", "Liver Function (Unknown)", "Complete Blood Count (Unknown)"]}, "Detailed Improvement Tips": ["\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "\ud83e\uddea To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations.", "\u2764\ufe0f To improve your cholesterol levels, focus on heart-healthy foods like fatty fish, nuts, olive oil, and fiber-rich fruits and vegetables.", "\ud83e\udec1 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor."]}
2025-05-19 16:38:43,918 - root - INFO - Saved real-time health score data for user f7666da2-1172-4e44-9670-a26202a18a56
2025-05-19 16:40:27,871 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:41:31,203 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:41:31,291 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Kidney Function": "Unknown", "Lipid Profile": "Unknown", "Lung Capacity": "Unknown", "Liver Function": "Unknown", "Complete Blood Count": "Unknown"}
2025-05-19 16:41:31,297 - root - INFO - Real-time health score result: {"Total Score": 78, "Health Status": "Good", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps. \ud83e\uddea To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations. \u2764\ufe0f To improve your cholesterol levels, focus on heart-healthy foods like fatty fish, nuts, olive oil, and fiber-rich fruits and vegetables. \ud83e\udec1 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor.", "timestamp": "2025-05-19T16:41:31.297678", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 12}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": ["Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)", "Voluntary Serology (Positive)", "Kidney Function (Unknown)", "Lipid Profile (Unknown)", "Lung Capacity (Unknown)", "Liver Function (Unknown)", "Complete Blood Count (Unknown)"]}, "Detailed Improvement Tips": ["\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "\ud83e\uddea To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations.", "\u2764\ufe0f To improve your cholesterol levels, focus on heart-healthy foods like fatty fish, nuts, olive oil, and fiber-rich fruits and vegetables.", "\ud83e\udec1 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor."]}
2025-05-19 16:41:31,304 - root - INFO - Saved real-time health score data for user f7666da2-1172-4e44-9670-a26202a18a56
2025-05-19 16:41:50,624 - root - INFO - Saved kidney function data for user f7666da2-1172-4e44-9670-a26202a18a56
2025-05-19 16:57:49,894 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:57:54,473 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 16:57:54,740 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Kidney Function": "Unknown", "Lipid Profile": "Unknown", "Lung Capacity": "Unknown", "Liver Function": "Unknown", "Complete Blood Count": "Unknown"}
2025-05-19 16:57:54,748 - root - INFO - Real-time health score result: {"Total Score": 78, "Health Status": "Good", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps. \ud83e\uddea To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations. \u2764\ufe0f To improve your cholesterol levels, focus on heart-healthy foods like fatty fish, nuts, olive oil, and fiber-rich fruits and vegetables. \ud83e\udec1 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor.", "timestamp": "2025-05-19T16:57:54.748452", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 12}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": ["Malaria (Positive)", "Widal Test (Positive)", "Hepatitis B (Positive)", "Voluntary Serology (Positive)", "Kidney Function (Unknown)", "Lipid Profile (Unknown)", "Lung Capacity (Unknown)", "Liver Function (Unknown)", "Complete Blood Count (Unknown)"]}, "Detailed Improvement Tips": ["\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "\ud83e\uddea To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations.", "\u2764\ufe0f To improve your cholesterol levels, focus on heart-healthy foods like fatty fish, nuts, olive oil, and fiber-rich fruits and vegetables.", "\ud83e\udec1 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor."]}
2025-05-19 16:57:54,749 - root - INFO - Saved real-time health score data for user f7666da2-1172-4e44-9670-a26202a18a56
2025-05-19 17:04:57,509 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 17:05:03,955 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 17:05:04,048 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-19 17:05:04,050 - root - INFO - Real-time health score result: {"Total Score": 100, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!", "timestamp": "2025-05-19T17:05:04.050998", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!"]}
2025-05-19 17:05:04,050 - root - INFO - Saved real-time health score data for user f7666da2-1172-4e44-9670-a26202a18a56
2025-05-19 17:05:28,795 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 17:05:31,075 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 17:05:31,104 - root - INFO - Processing vital signs: {"data": {"Blood_Pressure_Systolic": 120.0, "Blood_Pressure_Diastolic": 80.0, "Heart_Rate": 75.0, "Temperature": 36.8, "Glucose": 100.0, "SpO2": 98.0}}
2025-05-19 17:05:31,106 - root - INFO - Vital signs monitoring result: ✅ Body temperature is normal. Your body is functioning well.
✅ Glucose is within the normal range. Keep maintaining a balanced diet.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
2025-05-19 17:05:31,110 - root - INFO - Saved vital signs data for user f7666da2-1172-4e44-9670-a26202a18a56
2025-05-19 17:06:03,420 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 17:06:07,806 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 17:06:07,893 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-19 17:06:07,893 - root - INFO - Real-time health score result: {"Total Score": 100, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!", "timestamp": "2025-05-19T17:06:07.893443", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!"]}
2025-05-19 17:06:07,894 - root - INFO - Saved real-time health score data for user 8e1a4302-efe6-431f-afcb-ff3b58852889
2025-05-19 17:08:20,286 - root - INFO - Saved kidney function data for user 7ed3358d-0e4f-42d8-95d7-e7420718b393
2025-05-19 17:52:21,917 - root - INFO - Successfully imported health tools
2025-05-19 17:52:21,921 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 17:52:21,924 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 17:52:21,973 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 17:52:21,977 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 17:52:21,978 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 17:52:22,012 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 18:33:48,923 - root - INFO - Successfully imported health tools
2025-05-19 18:33:48,979 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 18:33:48,979 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 18:33:49,029 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 18:33:49,030 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 18:33:49,030 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 18:33:49,061 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 18:35:30,610 - root - INFO - Original health data: {"Glucose": 120, "SpO2": 94, "ECG (Heart Rate)": 85, "Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "Weight (BMI)": 26, "Temperature": 37}
2025-05-19 18:35:30,610 - root - INFO - Health data received: {"Glucose": 120, "SpO2": 94, "ECG (Heart Rate)": 85, "Blood Pressure (Systolic)": 130, "Blood Pressure (Diastolic)": 85, "Weight (BMI)": 26, "Temperature": 37}
2025-05-19 18:35:30,610 - root - INFO - Processed health data: {"Glucose": 120.0, "SpO2": 94.0, "ECG (Heart Rate)": 85.0, "Blood Pressure (Systolic)": 130.0, "Blood Pressure (Diastolic)": 85.0, "Weight (BMI)": 26.0, "Temperature": 37.0}
2025-05-19 18:35:30,610 - root - INFO - Initializing CustomHealthScoreAnalysisTool
2025-05-19 18:35:30,610 - root - INFO - Health score report generated: {"Total Score": 50, "Health Status": "Fair", "Vitals Needing Improvement": ["Glucose (Moderately High)", "SpO2 (Low)", "Blood Pressure (Systolic) (Abnormal)", "Blood Pressure (Diastolic) (Abnormal)", "Weight (BMI) (Moderately High)"], "Improvement Tips": ["\ud83c\udf4e To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.", "\ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.", "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83d\udeb6\u200d\u2640\ufe0f Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight."]}
2025-05-19 18:35:30,610 - root - INFO - Saved health score data for user test_user
2025-05-19 18:35:45,454 - root - INFO - Saved device recommendations for user test_user
2025-05-19 18:36:09,431 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-19 18:36:09,432 - root - INFO - Retrieved context: ...
2025-05-19 18:36:09,432 - root - INFO - Added health data context to system prompt for user test_user
2025-05-19 18:37:17,256 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-19 18:37:17,256 - root - INFO - Detected primary intent: device_recommendations with score 5.0
2025-05-19 18:37:59,885 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-19 18:37:59,887 - root - INFO - Retrieved context: ...
2025-05-19 18:37:59,888 - root - INFO - Added health data context to system prompt for user test_user
2025-05-19 18:39:04,238 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-19 18:39:04,239 - root - INFO - Detected primary intent: yes_confirmation with score 2.0
2025-05-19 18:39:04,239 - root - INFO - Confirmation detected, using previous intent: device_recommendations
2025-05-19 18:44:38,715 - root - INFO - Successfully imported health tools
2025-05-19 18:44:38,769 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 18:44:38,770 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 18:44:38,800 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 18:44:38,801 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 18:44:38,802 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 18:44:38,835 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 18:54:10,849 - root - INFO - Successfully imported health tools
2025-05-19 18:54:10,934 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 18:54:10,934 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 18:54:10,985 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 18:54:10,985 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 18:54:10,985 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 18:54:11,036 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 19:32:50,223 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 19:32:52,213 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 19:32:52,434 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-19 19:32:52,439 - root - INFO - Real-time health score result: {"Total Score": 100, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!", "timestamp": "2025-05-19T19:32:52.439030", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!"]}
2025-05-19 19:32:52,439 - root - INFO - Saved real-time health score data for user 737f3ca7-824c-4754-a9cd-c35864c064b4
2025-05-19 19:33:23,582 - root - INFO - Saved device recommendations for user 737f3ca7-824c-4754-a9cd-c35864c064b4
2025-05-19 19:35:02,181 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 19:35:44,954 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 20:48:42,904 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-19 20:48:42,908 - root - INFO - Retrieved context: ...
2025-05-19 20:48:47,078 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-19 20:48:47,078 - root - INFO - Detected primary intent: yes_confirmation with score 1.5
2025-05-19 20:49:03,830 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 20:49:45,578 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 20:49:45,650 - root - INFO - Processing vital signs: {"data": {"Blood_Pressure_Systolic": 130.0, "Blood_Pressure_Diastolic": 73.0, "Temperature": 35.1, "Glucose": 193.0}}
2025-05-19 20:49:45,654 - root - INFO - Vital signs monitoring result: ⚠️ Body temperature is low. This could indicate hypothermia. Stay warm and monitor your health.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
2025-05-19 20:49:45,655 - root - INFO - Saved vital signs data for user 72a6ab38-f28e-4d88-9beb-f6370e71d0a3
2025-05-19 20:49:58,650 - root - INFO - Saved device recommendations for user 72a6ab38-f28e-4d88-9beb-f6370e71d0a3
2025-05-19 21:07:31,855 - root - INFO - Successfully imported health tools
2025-05-19 21:07:31,879 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 21:07:31,895 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 21:07:31,937 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 21:07:31,954 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 21:07:31,957 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 21:07:32,011 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 21:14:04,162 - root - INFO - Successfully imported health tools
2025-05-19 21:14:04,177 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 21:14:04,177 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 21:14:04,214 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 21:14:04,214 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 21:14:04,214 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 21:14:04,252 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 21:19:20,892 - root - INFO - Successfully imported health tools
2025-05-19 21:19:20,892 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 21:19:20,892 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 21:19:20,940 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 21:19:20,941 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 21:19:20,941 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 21:19:21,001 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 21:23:45,066 - root - INFO - Saved symptom checker data for user 08977cff-3a75-4336-9b98-4b74d69a9119
2025-05-19 21:43:50,553 - root - INFO - Successfully imported health tools
2025-05-19 21:43:50,557 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 21:43:50,557 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 21:43:50,598 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 21:43:50,598 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 21:43:50,606 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 21:43:50,629 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 21:46:39,884 - root - INFO - Successfully imported health tools
2025-05-19 21:46:39,895 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 21:46:39,895 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 21:46:39,933 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 21:46:39,933 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 21:46:39,933 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 21:46:39,966 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 21:47:26,909 - root - INFO - Listing available lab tests
2025-05-19 21:47:52,956 - root - INFO - Explaining lab test: malaria test
2025-05-19 21:47:52,985 - root - INFO - Listing available lab tests
2025-05-19 21:48:16,084 - root - INFO - Explaining lab test: widal test
2025-05-19 21:48:16,137 - root - INFO - Listing available lab tests
2025-05-19 21:48:55,357 - root - INFO - Explaining lab test: Complete Blood COunt
2025-05-19 21:48:55,416 - root - INFO - Listing available lab tests
2025-05-19 21:51:54,909 - root - INFO - Listing available lab tests
2025-05-19 21:52:17,577 - root - INFO - Explaining lab test: Lipid Profile
2025-05-19 21:53:44,649 - root - INFO - Explaining lab test: Malaria Test
2025-05-19 23:14:09,444 - root - INFO - Successfully imported health tools
2025-05-19 23:14:09,447 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 23:14:09,464 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 23:14:09,512 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 23:14:09,523 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 23:14:09,530 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 23:14:09,586 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 23:19:08,778 - root - INFO - Successfully imported health tools
2025-05-19 23:19:08,787 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 23:19:08,787 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 23:19:08,828 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 23:19:08,828 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 23:19:08,830 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 23:19:08,861 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 23:25:02,864 - root - INFO - Successfully imported health tools
2025-05-19 23:25:02,864 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 23:25:02,864 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 23:25:02,897 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 23:25:02,911 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 23:25:02,912 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 23:25:02,953 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 23:27:06,360 - root - INFO - Successfully imported health tools
2025-05-19 23:27:06,360 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 23:27:06,360 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 23:27:06,399 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 23:27:06,399 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 23:27:06,400 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 23:27:06,445 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 23:29:29,368 - root - INFO - Successfully imported health tools
2025-05-19 23:29:29,380 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 23:29:29,382 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 23:29:29,419 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 23:29:29,419 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 23:29:29,419 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 23:29:29,455 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 23:31:48,543 - root - INFO - Successfully imported health tools
2025-05-19 23:31:48,543 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 23:31:48,543 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 23:31:48,595 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 23:31:48,598 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 23:31:48,598 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 23:31:48,636 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 23:36:20,451 - root - INFO - Successfully imported health tools
2025-05-19 23:36:20,465 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 23:36:20,465 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 23:36:20,533 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 23:36:20,533 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 23:36:20,533 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 23:36:20,593 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 23:55:27,444 - root - INFO - Successfully imported health tools
2025-05-19 23:55:27,446 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 23:55:27,446 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 23:55:27,484 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 23:55:27,484 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 23:55:27,484 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 23:55:27,534 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 23:56:24,735 - root - INFO - Successfully imported health tools
2025-05-19 23:56:24,735 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-19 23:56:24,740 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-19 23:56:24,783 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-19 23:56:24,783 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-19 23:56:24,783 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-19 23:56:24,819 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-19 23:59:03,883 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 23:59:06,012 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-19 23:59:06,106 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-19 23:59:06,106 - root - INFO - Real-time health score result: {"Total Score": 100, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!", "timestamp": "2025-05-19T23:59:06.106634", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!"]}
2025-05-19 23:59:06,112 - root - INFO - Saved real-time health score data for user ece7de9f-82c6-4a05-a725-7eff37563107
2025-05-20 17:51:51,590 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 17:52:16,052 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 17:52:16,156 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 141.0, "Blood Pressure (Diastolic)": 120.0, "ECG (Heart Rate)": 46.0, "SpO2": 98.0, "Temperature": 35.2, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-20 17:52:16,159 - root - INFO - Real-time health score result: {"Total Score": 77, "Health Status": "Good", "Vitals Needing Improvement": "Blood Pressure (Systolic) (High), Blood Pressure (Diastolic) (High), ECG (Heart Rate) (Low), Temperature (Low)", "Improvement Tips": "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "timestamp": "2025-05-20T17:52:16.159414", "Category Scores": {"Vitals": 42, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": ["Blood Pressure (Systolic) (High)", "Blood Pressure (Diastolic) (High)", "ECG (Heart Rate) (Low)", "Temperature (Low)"], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity."]}
2025-05-20 17:52:16,160 - root - INFO - Saved real-time health score data for user f128c854-2ec5-4c2b-b049-6c6f4e551e76
2025-05-20 17:52:45,809 - root - INFO - Saved device recommendations for user f128c854-2ec5-4c2b-b049-6c6f4e551e76
2025-05-20 18:13:58,511 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 18:14:51,733 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 18:14:51,807 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 46.0, "SpO2": 98.0, "Temperature": 35.5, "Weight (BMI)": 24.2, "Respiratory Rate": 14.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-20 18:14:51,822 - root - INFO - Real-time health score result: {"Total Score": 89, "Health Status": "Excellent", "Vitals Needing Improvement": "ECG (Heart Rate) (Low), Temperature (Low)", "Improvement Tips": "\ud83c\udf1f Some of your health metrics could use attention. Focus on a balanced diet, regular exercise, and adequate rest to improve your overall health.", "timestamp": "2025-05-20T18:14:51.822858", "Category Scores": {"Vitals": 74, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": ["ECG (Heart Rate) (Low)", "Temperature (Low)"], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83c\udf1f Some of your health metrics could use attention. Focus on a balanced diet, regular exercise, and adequate rest to improve your overall health."]}
2025-05-20 18:14:51,823 - root - INFO - Saved real-time health score data for user f128c854-2ec5-4c2b-b049-6c6f4e551e76
2025-05-20 18:35:27,157 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 18:35:51,798 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 18:35:51,837 - root - INFO - Processing vital signs: {"data": {"Blood_Pressure_Systolic": 120.0, "Blood_Pressure_Diastolic": 80.0, "Heart_Rate": 45.0, "Temperature": 36.8, "Glucose": 160.0, "SpO2": 86.0}}
2025-05-20 18:35:51,839 - root - INFO - Vital signs monitoring result: ✅ Body temperature is normal. Your body is functioning well.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
🚨 SpO2 is critically low. This could indicate respiratory issues. Seek medical attention immediately.
2025-05-20 18:35:51,841 - root - INFO - Saved vital signs data for user b36c93c3-8e28-44b1-9452-4a392f6c656f
2025-05-20 18:42:08,095 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 18:42:26,404 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 18:42:26,434 - root - INFO - Processing vital signs: {"data": {"Blood_Pressure_Systolic": 120.0, "Blood_Pressure_Diastolic": 80.0, "Heart_Rate": 46.0, "Temperature": 36.8, "Glucose": 181.0, "SpO2": 82.0}}
2025-05-20 18:42:26,440 - root - INFO - Vital signs monitoring result: ✅ Body temperature is normal. Your body is functioning well.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
🚨 SpO2 is critically low. This could indicate respiratory issues. Seek medical attention immediately.
2025-05-20 18:42:26,441 - root - INFO - Saved vital signs data for user 69220b40-e235-481d-8a81-c5a7a23a4b4b
2025-05-20 18:44:18,980 - root - INFO - Saved lipid profile data for user 69220b40-e235-481d-8a81-c5a7a23a4b4b
2025-05-20 19:03:47,624 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 19:04:08,820 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 19:04:08,863 - root - INFO - Processing vital signs: {"data": {"Blood_Pressure_Systolic": 125.0, "Blood_Pressure_Diastolic": 63.0, "Heart_Rate": 46.0, "Temperature": 36.8, "Glucose": 100.0, "SpO2": 98.0}}
2025-05-20 19:04:08,883 - root - INFO - Vital signs monitoring result: ✅ Body temperature is normal. Your body is functioning well.
✅ Glucose is within the normal range. Keep maintaining a balanced diet.
✅ SpO2 is normal. Your oxygen saturation levels are healthy.
2025-05-20 19:04:08,884 - root - INFO - Saved vital signs data for user f3b6be1b-5cba-42b9-8807-4c79a7c2fd59
2025-05-20 19:12:08,783 - root - INFO - Saved kidney function data for user 06640b2d-af3d-4650-90a5-6f545d997e95
2025-05-20 19:13:40,956 - root - INFO - Saved device recommendations for user 06640b2d-af3d-4650-90a5-6f545d997e95
2025-05-20 19:56:40,850 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 19:57:05,097 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 19:57:05,196 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 148.0, "Blood Pressure (Diastolic)": 120.0, "ECG (Heart Rate)": 45.0, "SpO2": 98.0, "Temperature": 35.1, "Weight (BMI)": 24.2, "Respiratory Rate": 10.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-20 19:57:05,207 - root - INFO - Real-time health score result: {"Total Score": 73, "Health Status": "Good", "Vitals Needing Improvement": "Blood Pressure (Systolic) (High), Blood Pressure (Diastolic) (High), ECG (Heart Rate) (Low), Temperature (Low), Respiratory Rate (Low)", "Improvement Tips": "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "timestamp": "2025-05-20T19:57:05.207600", "Category Scores": {"Vitals": 32, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": ["Blood Pressure (Systolic) (High)", "Blood Pressure (Diastolic) (High)", "ECG (Heart Rate) (Low)", "Temperature (Low)", "Respiratory Rate (Low)"], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity."]}
2025-05-20 19:57:05,209 - root - INFO - Saved real-time health score data for user ce98b24b-48d0-4c3a-98a0-008a66afb042
2025-05-20 19:58:29,845 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 19:58:55,056 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 19:58:55,100 - root - INFO - Processing vital signs: {"data": {"Blood_Pressure_Systolic": 120.0, "Blood_Pressure_Diastolic": 80.0, "Heart_Rate": 46.0, "Temperature": 35.1, "Glucose": 121.0, "SpO2": 86.0}}
2025-05-20 19:58:55,101 - root - INFO - Vital signs monitoring result: ⚠️ Body temperature is low. This could indicate hypothermia. Stay warm and monitor your health.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
🚨 SpO2 is critically low. This could indicate respiratory issues. Seek medical attention immediately.
2025-05-20 19:58:55,103 - root - INFO - Saved vital signs data for user ab15dfb5-820b-406e-ad92-5490b297b3e5
2025-05-20 20:04:42,902 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 20:04:49,767 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 20:04:49,900 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 45.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-20 20:04:49,902 - root - INFO - Real-time health score result: {"Total Score": 94, "Health Status": "Excellent", "Vitals Needing Improvement": "ECG (Heart Rate) (Low)", "Improvement Tips": "\ud83c\udf1f Some of your health metrics could use attention. Focus on a balanced diet, regular exercise, and adequate rest to improve your overall health.", "timestamp": "2025-05-20T20:04:49.902230", "Category Scores": {"Vitals": 84, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": ["ECG (Heart Rate) (Low)"], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83c\udf1f Some of your health metrics could use attention. Focus on a balanced diet, regular exercise, and adequate rest to improve your overall health."]}
2025-05-20 20:04:49,902 - root - INFO - Saved real-time health score data for user b9128444-0d3e-42a6-ab6c-c3e4f7c4547f
2025-05-20 20:07:59,988 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 20:08:06,838 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 20:08:24,891 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 20:08:25,031 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 45.0, "SpO2": 83.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 12.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-20 20:08:25,038 - root - INFO - Real-time health score result: {"Total Score": 87, "Health Status": "Excellent", "Vitals Needing Improvement": "ECG (Heart Rate) (Low), SpO2 (Low)", "Improvement Tips": "\ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.", "timestamp": "2025-05-20T20:08:25.038362", "Category Scores": {"Vitals": 68, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": ["ECG (Heart Rate) (Low)", "SpO2 (Low)"], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels."]}
2025-05-20 20:08:25,038 - root - INFO - Saved real-time health score data for user b9128444-0d3e-42a6-ab6c-c3e4f7c4547f
2025-05-20 20:16:51,207 - root - INFO - Processing lung capacity data: {"FEV1": 60.5, "FVC": 70.5, "FEV1_FVC_ratio": 0.4, "PEF": 55.0, "FEF25_75": 66.0, "Age": 40, "Height": 120.0, "Sex": "Male", "Race": "African American", "Smoking_Status": "Current smoker"}
2025-05-20 20:16:51,217 - root - INFO - Lung capacity analysis result: {"analysis": ["FEV1 is 60.5% of predicted value (0.65L, expected 1.07L)", "FVC is 70.5% of predicted value (0.98L, expected 1.39L)", "PEF is 55.0% of predicted value (215.03L/min, expected 390.96L/min)", "Based on your age, height, and demographics, your expected values are: FEV1 1.07L, FVC 1.39L, PEF 390.96L/min", "FEV1 is 60.5% of predicted value, indicating moderate airflow limitation.", "FVC is 70.5% of predicted value, indicating mild restrictive pattern.", "FEV1/FVC ratio is 0.4, indicating obstructive lung disease.", "PEF is 55.0% of predicted value, indicating reduced peak flow.", "FEF25-75 is 66.0% of predicted value, which is within normal range."], "respiratory_risk_level": "High", "potential_conditions": ["Obstructive lung disease", "Possible COPD"], "recommendations": ["\ud83e\udec1 Consider pulmonary function testing with bronchodilator response to confirm COPD diagnosis.", "\ud83d\udead Smoking cessation is essential to prevent further lung damage.", "\ud83d\udc68\u200d\u2695\ufe0f Consult with a pulmonologist for comprehensive COPD management.", "\ud83d\udca8 Practice breathing exercises to improve lung function.", "\ud83c\udfc3 Regular aerobic exercise can help improve respiratory capacity.", "\ud83d\ude37 Avoid respiratory irritants and air pollution when possible."], "confidence_level": "High", "missing_parameters": [], "doctor_summary": "Hi there! I've reviewed your spirometry results, and here's what I'm seeing: I'm seeing some significant concerns with your lung function that we should address. The test provided comprehensive data, so I'm quite confident in this assessment. Based on these patterns, I'm seeing indicators that could be associated with Obstructive lung disease, Possible COPD. Remember, these are preliminary findings based on your spirometry results, not a definitive diagnosis."}
2025-05-20 20:16:51,231 - root - INFO - Saved lung capacity data for user e7469448-6b2c-4719-895e-5feef6eb2c8c
2025-05-20 20:21:34,173 - root - INFO - Saved device recommendations for user e7469448-6b2c-4719-895e-5feef6eb2c8c
2025-05-20 20:30:30,939 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 20:30:34,044 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 20:30:34,131 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-20 20:30:34,131 - root - INFO - Real-time health score result: {"Total Score": 100, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!", "timestamp": "2025-05-20T20:30:34.131292", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!"]}
2025-05-20 20:30:34,131 - root - INFO - Saved real-time health score data for user c8490dd3-5f5e-4cad-a112-a3e031035855
2025-05-20 20:31:01,312 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 20:31:57,042 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-20 20:31:57,140 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 46.0, "SpO2": 80.0, "Temperature": 35.1, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-20 20:31:57,143 - root - INFO - Real-time health score result: {"Total Score": 83, "Health Status": "Good", "Vitals Needing Improvement": "ECG (Heart Rate) (Low), SpO2 (Low), Temperature (Low)", "Improvement Tips": "\ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.", "timestamp": "2025-05-20T20:31:57.143998", "Category Scores": {"Vitals": 58, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": ["ECG (Heart Rate) (Low)", "SpO2 (Low)", "Temperature (Low)"], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels."]}
2025-05-20 20:31:57,145 - root - INFO - Saved real-time health score data for user e7469448-6b2c-4719-895e-5feef6eb2c8c
2025-05-20 20:32:12,122 - root - INFO - Saved device recommendations for user e7469448-6b2c-4719-895e-5feef6eb2c8c
2025-05-20 20:33:16,281 - root - INFO - Saved device recommendations for user c8490dd3-5f5e-4cad-a112-a3e031035855
2025-05-20 20:35:41,948 - root - INFO - Successfully imported health tools
2025-05-20 20:35:41,956 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-20 20:35:41,967 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-20 20:35:42,014 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-20 20:35:42,044 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-20 20:35:42,048 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-20 20:35:42,109 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-21 12:34:42,810 - root - INFO - Successfully imported health tools
2025-05-21 12:34:42,816 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-21 12:34:42,833 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-21 12:34:42,882 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-21 12:34:42,894 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-21 12:34:42,902 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-21 12:34:42,951 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-21 12:35:35,267 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-21 12:36:44,587 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-21 12:36:44,676 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 135.0, "Blood Pressure (Diastolic)": 110.0, "ECG (Heart Rate)": 110.0, "SpO2": 85.0, "Temperature": 36.8, "Weight (BMI)": 13.9, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 56.0, "Malaria": "Positive", "Widal Test": "Positive", "Hepatitis B": "Negative"}
2025-05-21 12:36:44,678 - root - INFO - Real-time health score result: {"Total Score": 45, "Health Status": "Poor", "Vitals Needing Improvement": "Blood Pressure (Systolic) (High), Blood Pressure (Diastolic) (High), ECG (Heart Rate) (High), SpO2 (Low), Weight (BMI) (Low)", "Improvement Tips": "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity. \ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels. \ud83e\udd57 Consider adding more nutrient-dense foods like nuts, avocados, and protein-rich meals to help you reach a healthier weight. \ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "timestamp": "2025-05-21T12:36:44.678813", "Category Scores": {"Vitals": 12, "Lifestyle": 100, "Test Results": 20}, "Detailed Issues": {"Vitals": ["Blood Pressure (Systolic) (High)", "Blood Pressure (Diastolic) (High)", "ECG (Heart Rate) (High)", "SpO2 (Low)", "Weight (BMI) (Low)"], "Lifestyle": [], "Test Results": ["Glucose (Low)", "Malaria (Positive)", "Widal Test (Positive)"]}, "Detailed Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.", "\ud83e\udd57 Consider adding more nutrient-dense foods like nuts, avocados, and protein-rich meals to help you reach a healthier weight.", "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-21 12:36:44,678 - root - INFO - Saved real-time health score data for user 737be4b9-c384-4e0c-8fc2-13c1f01b44ac
2025-05-21 12:42:58,450 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-21 12:42:58,462 - root - INFO - Retrieved context: ...
2025-05-21 12:42:58,464 - root - INFO - Added health data context to system prompt for user 737be4b9-c384-4e0c-8fc2-13c1f01b44ac
2025-05-21 12:43:19,618 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-21 13:06:48,556 - root - INFO - Successfully imported health tools
2025-05-21 13:06:48,563 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-21 13:06:48,566 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-21 13:06:48,604 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-21 13:06:48,606 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-21 13:06:48,606 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-21 13:06:48,648 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-21 13:09:02,911 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-21 13:11:10,285 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-21 13:11:10,393 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 142.0, "Blood Pressure (Diastolic)": 110.0, "SpO2": 100.0, "Temperature": 36.8, "Weight (BMI)": 27.1, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 94.0, "Malaria": "Negative", "Widal Test": "Positive"}
2025-05-21 13:11:10,393 - root - INFO - Real-time health score result: {"Total Score": 71, "Health Status": "Good", "Vitals Needing Improvement": "Blood Pressure (Systolic) (High), Blood Pressure (Diastolic) (High), Weight (BMI) (High)", "Improvement Tips": "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity. \ud83d\udcaa Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle. \ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "timestamp": "2025-05-21T13:11:10.393178", "Category Scores": {"Vitals": 36, "Lifestyle": 100, "Test Results": 85}, "Detailed Issues": {"Vitals": ["Blood Pressure (Systolic) (High)", "Blood Pressure (Diastolic) (High)", "Weight (BMI) (High)"], "Lifestyle": [], "Test Results": ["Widal Test (Positive)"]}, "Detailed Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83d\udcaa Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle.", "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-21 13:11:10,398 - root - INFO - Saved real-time health score data for user 737be4b9-c384-4e0c-8fc2-13c1f01b44ac
2025-05-21 13:19:08,714 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-21 13:20:50,939 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-21 13:20:51,018 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 139.0, "Blood Pressure (Diastolic)": 112.0, "Temperature": 35.0, "Weight (BMI)": 33.5, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 80.0, "Malaria": "Positive", "Widal Test": "Positive"}
2025-05-21 13:20:51,018 - root - INFO - Real-time health score result: {"Total Score": 83, "Health Status": "Good", "Vitals Needing Improvement": "Blood Pressure (Systolic) (High), Blood Pressure (Diastolic) (High), Temperature (Low), Weight (BMI) (High)", "Improvement Tips": "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity. \ud83d\udcaa Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle. \ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.", "timestamp": "2025-05-21T13:20:51.018463", "Category Scores": {"Vitals": 0, "Lifestyle": 100, "Test Results": 60}, "Detailed Issues": {"Vitals": ["Blood Pressure (Systolic) (High)", "Blood Pressure (Diastolic) (High)", "Temperature (Low)", "Weight (BMI) (High)"], "Lifestyle": [], "Test Results": ["Malaria (Positive)", "Widal Test (Positive)"]}, "Detailed Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83d\udcaa Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle.", "\ud83e\ude7a Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps."]}
2025-05-21 13:20:51,028 - root - INFO - Saved real-time health score data for user 737be4b9-c384-4e0c-8fc2-13c1f01b44ac
2025-05-21 13:21:02,265 - root - INFO - Saved device recommendations for user 737be4b9-c384-4e0c-8fc2-13c1f01b44ac with test_type: None
2025-05-21 13:22:17,219 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-21 13:22:17,219 - root - INFO - Retrieved context: ...
2025-05-21 13:22:17,222 - root - INFO - Added health data context to system prompt for user 737be4b9-c384-4e0c-8fc2-13c1f01b44ac
2025-05-21 13:23:55,615 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-21 13:27:47,948 - root - INFO - Saved symptom checker data for user 737be4b9-c384-4e0c-8fc2-13c1f01b44ac
2025-05-21 13:30:12,395 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-21 13:30:12,396 - root - INFO - Retrieved context: ...
2025-05-21 13:30:12,396 - root - INFO - Added health data context to system prompt for user 737be4b9-c384-4e0c-8fc2-13c1f01b44ac
2025-05-21 13:31:25,437 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-21 13:33:48,849 - root - INFO - Listing available lab tests
2025-05-21 13:34:26,472 - root - INFO - Explaining lab test: lipid profile
2025-05-21 13:50:08,492 - root - INFO - Successfully imported health tools
2025-05-21 13:50:08,500 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-21 13:50:08,500 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-21 13:50:08,559 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-21 13:50:08,559 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-21 13:50:08,559 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-21 13:50:08,652 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-21 13:58:00,534 - root - INFO - Listing available lab tests
2025-05-21 13:59:07,268 - root - INFO - Successfully imported health tools
2025-05-21 13:59:07,268 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-21 13:59:07,268 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-21 13:59:07,316 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-21 13:59:07,316 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-21 13:59:07,319 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-21 13:59:07,355 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-21 13:59:41,878 - root - INFO - Listing available lab tests
2025-05-21 14:05:35,208 - root - INFO - Successfully imported health tools
2025-05-21 14:05:35,209 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-21 14:05:35,212 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-21 14:05:35,261 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-21 14:05:35,261 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-21 14:05:35,261 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-21 14:05:35,298 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-21 14:10:24,850 - root - INFO - Explaining lab test: widal test
2025-05-21 14:11:11,218 - root - INFO - Explaining lab test: malaria test
2025-05-21 14:11:31,278 - root - INFO - Explaining lab test: diabetics
2025-05-21 14:12:21,696 - root - INFO - Explaining lab test: kidney function test
2025-05-21 14:13:07,052 - root - INFO - Explaining lab test: kidney function
2025-05-21 21:16:56,539 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-21 21:17:29,334 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-21 21:17:29,372 - root - INFO - Processing vital signs: {"data": {"Blood_Pressure_Systolic": 120.0, "Blood_Pressure_Diastolic": 80.0, "Heart_Rate": 130.0, "Temperature": 36.8, "Glucose": 142.0, "SpO2": 85.0}}
2025-05-21 21:17:29,390 - root - INFO - Vital signs monitoring result: ✅ Body temperature is normal. Your body is functioning well.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
🚨 SpO2 is critically low. This could indicate respiratory issues. Seek medical attention immediately.
2025-05-21 21:17:29,405 - root - INFO - Saved vital signs data for user 394b49f2-8199-43de-8f6f-457860269135
2025-05-22 14:52:20,858 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-22 14:52:55,352 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-22 14:52:55,436 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 128.0, "Blood Pressure (Diastolic)": 105.0, "SpO2": 85.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 24.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-22 14:52:55,453 - root - INFO - Real-time health score result: {"Total Score": 72, "Health Status": "Good", "Vitals Needing Improvement": "Blood Pressure (Systolic) (High), Blood Pressure (Diastolic) (High), SpO2 (Low), Respiratory Rate (High)", "Improvement Tips": "\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity. \ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.", "timestamp": "2025-05-22T14:52:55.453901", "Category Scores": {"Vitals": 31, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": ["Blood Pressure (Systolic) (High)", "Blood Pressure (Diastolic) (High)", "SpO2 (Low)", "Respiratory Rate (High)"], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83e\ude78 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.", "\ud83e\udec1 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels."]}
2025-05-22 14:52:55,520 - root - INFO - Saved real-time health score data for user 19fd1a1f-78b7-4ac4-a892-974180082367
2025-05-22 14:54:58,335 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-22 14:54:58,384 - root - INFO - Retrieved context: ...
2025-05-22 14:54:58,384 - root - INFO - Added health data context to system prompt for user 19fd1a1f-78b7-4ac4-a892-974180082367
2025-05-22 14:55:18,106 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-22 14:56:01,015 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-22 14:56:16,457 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-22 14:56:16,505 - root - INFO - Processing vital signs: {"data": {"Blood_Pressure_Systolic": 120.0, "Blood_Pressure_Diastolic": 80.0, "Heart_Rate": 46.0, "Temperature": 36.8, "Glucose": 232.0, "SpO2": 84.0}}
2025-05-22 14:56:16,505 - root - INFO - Vital signs monitoring result: ✅ Body temperature is normal. Your body is functioning well.
⚠️ Glucose is high. This could indicate prediabetes or diabetes. Monitor your diet and consult a doctor.
🚨 SpO2 is critically low. This could indicate respiratory issues. Seek medical attention immediately.
2025-05-22 14:56:16,513 - root - INFO - Saved vital signs data for user 19fd1a1f-78b7-4ac4-a892-974180082367
2025-05-22 14:58:15,216 - root - INFO - Explaining lab test: lipid profile test
2025-05-22 14:59:26,813 - root - INFO - Explaining lab test: widal test
2025-05-22 17:08:30,180 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test"], "test_name": "Widal Test", "patient_age": 40, "patient_sex": "Male", "salmonella_typhi_o": "Non-reactive", "salmonella_typhi_h": "Non-reactive", "salmonella_paratyphi_a": "Non-reactive", "salmonella_paratyphi_b": "Non-reactive", "test_result": "Negative"}
2025-05-22 17:08:30,198 - root - INFO - Test results interpretation: {"timestamp": "2025-05-22T17:08:30.198035", "tests_analyzed": [], "interpretation": [], "recommendations": [], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-22 17:08:30,198 - root - INFO - Saved test results data for user fe46e6ab-8999-43fb-8b29-7883aecb85b4
2025-05-22 17:12:26,093 - root - INFO - Successfully imported health tools
2025-05-22 17:12:26,094 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 17:12:26,111 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 17:12:26,150 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 17:12:26,167 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 17:12:26,171 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 17:12:26,206 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 17:13:49,074 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test"], "test_name": "Widal Test", "patient_age": 40, "patient_sex": "Male", "salmonella_typhi_o": "Non-reactive", "salmonella_typhi_h": "Non-reactive", "salmonella_paratyphi_a": "Non-reactive", "salmonella_paratyphi_b": "Non-reactive", "test_result": "Negative"}
2025-05-22 17:13:49,079 - root - INFO - Test results interpretation: {"timestamp": "2025-05-22T17:13:49.079834", "tests_analyzed": [], "interpretation": [], "recommendations": [], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-22 17:13:49,082 - root - INFO - Saved test results data for user 000bc43e-079d-4733-a155-7042effd20da
2025-05-22 17:16:38,117 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "test_name": "Malaria Test", "patient_age": 40, "patient_sex": "Male", "test_result": "Negative"}
2025-05-22 17:16:38,131 - root - INFO - Test results interpretation: {"timestamp": "2025-05-22T17:16:38.131262", "tests_analyzed": ["Malaria Test"], "interpretation": ["Your malaria test result is inconclusive or not available."], "recommendations": ["\ud83d\udd04 Consider retesting to obtain a clear result, especially if you have symptoms like fever, chills, headache, or body aches."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-22 17:16:38,132 - root - INFO - Saved test results data for user 000bc43e-079d-4733-a155-7042effd20da
2025-05-22 17:17:35,063 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test"], "test_name": "Widal Test", "patient_age": 40, "patient_sex": "Male", "salmonella_typhi_o": "Non-reactive", "salmonella_typhi_h": "Non-reactive", "salmonella_paratyphi_a": "Non-reactive", "salmonella_paratyphi_b": "Non-reactive", "test_result": "Negative"}
2025-05-22 17:17:35,063 - root - INFO - Test results interpretation: {"timestamp": "2025-05-22T17:17:35.063209", "tests_analyzed": [], "interpretation": [], "recommendations": [], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-22 17:17:35,072 - root - INFO - Saved test results data for user 000bc43e-079d-4733-a155-7042effd20da
2025-05-22 17:41:42,936 - root - INFO - Successfully imported health tools
2025-05-22 17:41:42,943 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 17:41:42,944 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 17:41:42,976 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 17:41:42,976 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 17:41:42,976 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 17:41:43,016 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 17:44:02,527 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "test_name": "Malaria Test", "patient_age": 30, "patient_sex": "Male", "test_result": "Positive", "parasite_species": "P. falciparum", "parasite_density": "High"}
2025-05-22 17:44:02,527 - root - INFO - Test results interpretation: {"timestamp": "2025-05-22T17:44:02.527602", "tests_analyzed": ["Malaria Test"], "interpretation": ["Your malaria test result is inconclusive or not available."], "recommendations": ["\ud83d\udd04 Consider retesting to obtain a clear result, especially if you have symptoms like fever, chills, headache, or body aches."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-22 17:44:02,529 - root - INFO - Saved test results data for user test_user
2025-05-22 17:44:17,041 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test"], "test_name": "Widal Test", "patient_age": 30, "patient_sex": "Male", "salmonella_typhi_o": "Reactive", "salmonella_typhi_h": "Reactive", "salmonella_paratyphi_a": "Non-reactive", "salmonella_paratyphi_b": "Non-reactive", "test_result": "Positive"}
2025-05-22 17:44:17,041 - root - INFO - Test results interpretation: {"timestamp": "2025-05-22T17:44:17.041041", "tests_analyzed": [], "interpretation": [], "recommendations": [], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-22 17:44:17,041 - root - INFO - Saved test results data for user test_user
2025-05-22 17:46:03,294 - root - INFO - Successfully imported health tools
2025-05-22 17:46:03,295 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 17:46:03,295 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 17:46:03,327 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 17:46:03,327 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 17:46:03,327 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 17:46:03,369 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 17:46:39,820 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "test_name": "Widal Test", "patient_age": 30, "patient_sex": "Male", "test_result": "Positive", "widal": {"Typhi O": "Reactive", "Typhi H": "Reactive", "Paratyphi A,H": "Non-reactive", "Paratyphi B,H": "Non-reactive"}}
2025-05-22 17:46:39,827 - root - INFO - Test results interpretation: {"timestamp": "2025-05-22T17:46:39.825042", "tests_analyzed": ["Widal Test (Typhoid)"], "interpretation": ["Your Widal test shows reactivity to Typhi O, Typhi H.", "Reactivity to both Typhi O and Typhi H antigens strongly suggests typhoid fever infection. This indicates your immune system is responding to Salmonella Typhi bacteria.", "The O (somatic) antigen reactivity indicates current infection, while H (flagellar) antigen reactivity suggests your immune system has been mounting a response for some time."], "recommendations": ["\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider within 24-48 hours for appropriate antibiotic treatment.", "\ud83e\ude7a Treatment typically includes antibiotics such as fluoroquinolones (e.g., ciprofloxacin), third-generation cephalosporins (e.g., ceftriaxone), or azithromycin.", "\ud83c\udf21\ufe0f Monitor your temperature every 4-6 hours. Fever in typhoid typically rises gradually and may reach 103-104\u00b0F (39-40\u00b0C).", "\ud83d\udca7 Maintain proper hydration with clean water or oral rehydration solution, especially if you have fever or diarrhea.", "\ud83e\uddfc Practice strict hand hygiene, especially before eating and after using the toilet.", "\ud83e\udd64 Drink only purified or boiled water and avoid raw foods that may be contaminated.", "\ud83c\udf7d\ufe0f Avoid eating food from street vendors or restaurants with questionable hygiene practices.", "\ud83d\udeab Avoid preparing food for others until cleared by a healthcare provider to prevent spreading the infection."], "urgency_level": "Medium", "flagged_results": ["Widal Test (Typhoid)"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some results that should be discussed with a healthcare provider. Let me explain what these results mean and what steps you should consider taking.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-22 17:46:39,830 - root - INFO - Saved test results data for user test_user
2025-05-22 17:47:08,355 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "test_name": "Malaria Test", "patient_age": 30, "patient_sex": "Male", "malaria": "Positive", "malaria_species": "P. falciparum", "parasite_density": "High"}
2025-05-22 17:47:08,360 - root - INFO - Test results interpretation: {"timestamp": "2025-05-22T17:47:08.355527", "tests_analyzed": ["Malaria Test"], "interpretation": ["Your malaria test is positive for P. falciparum, which is the most severe form of malaria and can lead to complications if not treated promptly."], "recommendations": ["\ud83e\udd9f Use bed nets and insect repellent to prevent mosquito bites.", "\ud83d\udca7 Eliminate standing water around your home to reduce mosquito breeding sites.", "\ud83c\udf21\ufe0f Monitor your temperature regularly and stay well-hydrated.", "\ud83e\ude7a Treatment typically includes artemisinin-based combination therapies (ACTs).", "\u26a0\ufe0f P. falciparum can rapidly lead to severe malaria, so prompt treatment is essential."], "urgency_level": "High", "flagged_results": ["Malaria Test"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm seeing some concerning results that require immediate medical attention. Let me explain what these results mean and what steps you should consider taking.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-22 17:47:08,360 - root - INFO - Saved test results data for user test_user
2025-05-22 17:53:22,006 - root - INFO - Successfully imported health tools
2025-05-22 17:53:22,008 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 17:53:22,008 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 17:53:22,044 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 17:53:22,044 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 17:53:22,044 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 17:53:22,081 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 17:54:32,151 - root - INFO - Received chronic condition tracking request: user_id='test_user' condition_data={'condition_type': 'diabetes', 'glucose': 120, 'hba1c': 6.5, 'medication_adherence': 'high', 'symptoms': ['frequent urination']} tracking_frequency='daily' measurement_date='2023-05-22'
2025-05-22 17:54:32,151 - root - INFO - Processing chronic tracker data for user test_user: {'condition_type': 'diabetes', 'glucose': 120, 'hba1c': 6.5, 'medication_adherence': 'high', 'symptoms': ['frequent urination'], 'tracking_frequency': 'daily', 'measurement_date': '2023-05-22'}
2025-05-22 17:54:32,151 - root - INFO - Condition type: diabetes
2025-05-22 17:54:32,151 - root - INFO - Calling chronic_tracker_tool with input: {"user_id": "test_user", "condition_data": {"condition_type": "diabetes", "glucose": 120, "hba1c": 6.5, "medication_adherence": "high", "symptoms": ["frequent urination"], "tracking_frequency": "daily", "measurement_date": "2023-05-22"}, "historical_data": []}
2025-05-22 17:54:32,165 - root - INFO - Received raw result from chronic_tracker_tool: {
  "current_analysis": {
    "glucose": {
      "status": "elevated",
      "description": "Your blood glucose is elevated, indicating prediabetes.",
      "risk_level": "moderate"
    },
    "hba1c": {
      "status": "high",
      "description": "Your HbA1c is high, indicating diabetes.",
      "risk_level": "high"
    },
    "medication_adherence": {
      "status": "good",
      "description": "You're taking your medications as prescribed. Great job!"
    },
    "symptoms": {
      "reported": [
        "frequent urination"
      ],
      "concerns": [
        "frequent urination"
      ]
    }
  },
  "trend_analysis": {},
  "recommendations": [
    "Follow your medication regimen as prescribed by your healthcare provider.",
    "Monitor your blood glucose regularly.",
    "Schedule regular check-ups with your healthcare provider.",
    "Schedule an appointment with your healthcare provider to discuss your symptoms.",
    "Maintain a balanced diet rich in vegetables, lean proteins, and whole grains.",
    "Stay physically active with at least 150 minutes of moderate exercise per week.",
    "Monitor your blood glucose regularly and keep a log of your readings.",
    "Take medications as prescribed and don't skip doses.",
    "\nFor better tracking:",
    "Track when these symptoms occur and their severity to share with your healthcare provider.",
    "\nFor continuous care:",
    "Consider daily glucose monitoring to better manage your diabetes."
  ],
  "summary": "Based on your diabetes tracking data, here's what I'm seeing:\n\nYour blood glucose is elevated. Your blood glucose is elevated, indicating prediabetes.\n\nYour HbA1c is high. Your HbA1c is high, indicating diabetes.\n\nI notice you're experiencing some concerning symptoms that we should address: frequent urination.\n\nHere are my recommendations:\n1. Follow your medication regimen as prescribed by your healthcare provider.\n2. Monitor your blood glucose regularly.\n3. Schedule regular check-ups with your healthcare provider.\n4. Schedule an appointment with your healthcare provider to discuss your symptoms.\n5. Maintain a balanced diet rich in vegetables, lean proteins, and whole grains.\n\nFor better tracking:\n\u2022 Track when these symptoms occur and their severity to share with your healthcare provider.\n\nFor continuous care:\n\u2022 Consider daily glucose monitoring to better manage your diabetes.\n"
}
2025-05-22 17:54:32,167 - root - INFO - Parsed result: {"current_analysis": {"glucose": {"status": "elevated", "description": "Your blood glucose is elevated, indicating prediabetes.", "risk_level": "moderate"}, "hba1c": {"status": "high", "description": "Your HbA1c is high, indicating diabetes.", "risk_level": "high"}, "medication_adherence": {"status": "good", "description": "You're taking your medications as prescribed. Great job!"}, "symptoms": {"reported": ["frequent urination"], "concerns": ["frequent urination"]}}, "trend_analysis": {}, "recommendations": ["Follow your medication regimen as prescribed by your healthcare provider.", "Monitor your blood glucose regularly.", "Schedule regular check-ups with your healthcare provider.", "Schedule an appointment with your healthcare provider to discuss your symptoms.", "Maintain a balanced diet rich in vegetables, lean proteins, and whole grains.", "Stay physically active with at least 150 minutes of moderate exercise per week.", "Monitor your blood glucose regularly and keep a log of your readings.", "Take medications as prescribed and don't skip doses.", "\nFor better tracking:", "Track when these symptoms occur and their severity to share with your healthcare provider.", "\nFor continuous care:", "Consider daily glucose monitoring to better manage your diabetes."], "summary": "Based on your diabetes tracking data, here's what I'm seeing:\n\nYour blood glucose is elevated. Your blood glucose is elevated, indicating prediabetes.\n\nYour HbA1c is high. Your HbA1c is high, indicating diabetes.\n\nI notice you're experiencing some concerning symptoms that we should address: frequent urination.\n\nHere are my recommendations:\n1. Follow your medication regimen as prescribed by your healthcare provider.\n2. Monitor your blood glucose regularly.\n3. Schedule regular check-ups with your healthcare provider.\n4. Schedule an appointment with your healthcare provider to discuss your symptoms.\n5. Maintain a balanced diet rich in vegetables, lean proteins, and whole grains.\n\nFor better tracking:\n\u2022 Track when these symptoms occur and their severity to share with your healthcare provider.\n\nFor continuous care:\n\u2022 Consider daily glucose monitoring to better manage your diabetes.\n"}
2025-05-22 17:54:32,167 - root - INFO - Saved chronic tracker data for user test_user
2025-05-22 21:15:38,650 - root - INFO - Successfully imported health tools
2025-05-22 21:15:38,655 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 21:15:38,656 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 21:15:38,709 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 21:15:38,709 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 21:15:38,709 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 21:15:38,763 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 21:15:38,828 - fastapi - ERROR - Form data requires "python-multipart" to be installed. 
You can install "python-multipart" with: 

pip install python-multipart

2025-05-22 21:17:05,932 - root - INFO - Successfully imported health tools
2025-05-22 21:17:05,933 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 21:17:05,933 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 21:17:05,997 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 21:17:06,005 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 21:17:06,005 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 21:17:06,097 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 21:27:56,410 - root - INFO - Successfully imported health tools
2025-05-22 21:27:56,410 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 21:27:56,411 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 21:27:56,442 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 21:27:56,442 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 21:27:56,442 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 21:27:56,477 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 21:41:22,621 - root - ERROR - Failed to import tools: No module named 'tools.tools_auto_bio_completer'
2025-05-22 21:43:46,865 - root - ERROR - Failed to import tools: No module named 'PyPDF2'
2025-05-22 21:46:26,025 - root - INFO - Successfully imported health tools
2025-05-22 21:46:26,034 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 21:46:26,034 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 21:46:26,059 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 21:46:26,059 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 21:46:26,059 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 21:46:26,120 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 21:47:22,127 - root - INFO - Successfully imported health tools
2025-05-22 21:47:22,127 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 21:47:22,131 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 21:47:22,160 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 21:47:22,160 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 21:47:22,160 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 21:47:22,204 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 22:40:31,727 - root - INFO - Successfully imported health tools
2025-05-22 22:40:31,727 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 22:40:31,727 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 22:40:31,775 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 22:40:31,775 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 22:40:31,782 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 22:40:31,825 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 22:49:22,645 - root - INFO - Successfully imported health tools
2025-05-22 22:49:22,646 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 22:49:22,646 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 22:49:22,663 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 22:49:22,677 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 22:49:22,679 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 22:49:22,722 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 22:56:46,064 - root - INFO - Successfully imported health tools
2025-05-22 22:56:46,064 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-22 22:56:46,070 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-22 22:56:46,100 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-22 22:56:46,100 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-22 22:56:46,101 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-22 22:56:46,131 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-22 22:57:29,763 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-22 22:57:36,108 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-22 22:57:48,133 - root - INFO - Processing test results data: {"selected_tests": ["Malaria Test"], "test_name": "Malaria Test", "patient_age": 40, "patient_sex": "Male", "malaria": "Negative"}
2025-05-22 22:57:48,142 - root - INFO - Test results interpretation: {"timestamp": "2025-05-22T22:57:48.142077", "tests_analyzed": ["Malaria Test"], "interpretation": ["Your malaria test is negative, suggesting no detectable malaria parasites in your blood at this time.", "Note that early infections may not always be detected. If symptoms persist, consider retesting in 24-48 hours."], "recommendations": ["\ud83e\udd9f Continue using preventive measures like bed nets and insect repellent in malaria-endemic areas."], "urgency_level": "Low", "flagged_results": [], "doctor_visit_recommended": false, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: Good news! Your test results look normal. This is reassuring, but remember that these tests are just one part of your overall health picture.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-22 22:57:48,160 - root - INFO - Saved test results data for user a9881810-05a4-4a83-90b4-25d0cbd9b841
2025-05-22 23:55:37,713 - root - INFO - Received profile completeness check request for user eefff7f5-27c3-4b25-b416-969d6acc53ec
2025-05-22 23:55:37,713 - root - INFO - Checking profile completeness for user eefff7f5-27c3-4b25-b416-969d6acc53ec
2025-05-23 00:02:16,932 - root - INFO - Updated progress history for user: ff820097-ba04-49e5-8ce3-0b9cde37ceed
2025-05-23 18:24:29,574 - root - INFO - Successfully imported health tools
2025-05-23 18:24:29,587 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-23 18:24:29,602 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-23 18:24:29,653 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-23 18:24:29,669 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-23 18:24:29,675 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-23 18:24:29,717 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-23 18:32:57,151 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-23 18:33:00,015 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-23 18:33:00,181 - root - INFO - Processing real-time health score data: {"Blood Pressure (Systolic)": 120.0, "Blood Pressure (Diastolic)": 80.0, "ECG (Heart Rate)": 75.0, "SpO2": 98.0, "Temperature": 36.8, "Weight (BMI)": 24.2, "Respiratory Rate": 16.0, "Exercise": 3.0, "Diet": 3.0, "Sleep": 7.0, "Stress": 2.0, "Hydration": 8.0, "Smoking": 0.0, "Alcohol": 1.0, "Social Connection": 3.0, "Glucose": 100.0}
2025-05-23 18:33:00,208 - root - INFO - Real-time health score result: {"Total Score": 100, "Health Status": "Excellent", "Vitals Needing Improvement": "None", "Improvement Tips": "\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!", "timestamp": "2025-05-23T18:33:00.208837", "Category Scores": {"Vitals": 100, "Lifestyle": 100, "Test Results": 100}, "Detailed Issues": {"Vitals": [], "Lifestyle": [], "Test Results": []}, "Detailed Improvement Tips": ["\ud83c\udf1f Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!"]}
2025-05-23 18:33:00,209 - root - INFO - Saved real-time health score data for user efa5e983-6384-4f7d-9609-619581374e3d
2025-05-23 18:34:30,724 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-23 18:34:30,742 - root - INFO - Retrieved context: ...
2025-05-23 18:34:30,744 - root - INFO - Added health data context to system prompt for user efa5e983-6384-4f7d-9609-619581374e3d
2025-05-23 18:34:41,174 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-23 19:43:08,225 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "test_name": "Widal Test", "patient_age": 40, "patient_sex": "Male", "test_result": "Positive", "widal": {"Typhi O": "Reactive", "Typhi H": "Reactive", "Paratyphi A,H": "Reactive", "Paratyphi B,H": "Reactive"}}
2025-05-23 19:43:08,230 - root - INFO - Test results interpretation: {"timestamp": "2025-05-23T19:43:08.230270", "tests_analyzed": ["Widal Test (Typhoid)"], "interpretation": ["Your Widal test shows reactivity to Typhi O, Typhi H, Paratyphi A,H, Paratyphi B,H.", "Reactivity to both Typhi O and Typhi H antigens strongly suggests typhoid fever infection. This indicates your immune system is responding to Salmonella Typhi bacteria.", "The O (somatic) antigen reactivity indicates current infection, while H (flagellar) antigen reactivity suggests your immune system has been mounting a response for some time.", "Reactivity to Paratyphi A,H, Paratyphi B,H suggests possible paratyphoid fever, caused by Salmonella Paratyphi. This illness is similar to typhoid but often presents with milder symptoms.", "Reactivity to multiple Paratyphi antigens suggests a significant infection that requires medical attention."], "recommendations": ["\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider within 24-48 hours for appropriate antibiotic treatment.", "\ud83e\ude7a Treatment typically includes antibiotics such as fluoroquinolones (e.g., ciprofloxacin), third-generation cephalosporins (e.g., ceftriaxone), or azithromycin.", "\ud83c\udf21\ufe0f Monitor your temperature every 4-6 hours. Fever in typhoid typically rises gradually and may reach 103-104\u00b0F (39-40\u00b0C).", "\ud83d\udca7 Maintain proper hydration with clean water or oral rehydration solution, especially if you have fever or diarrhea.", "\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider for evaluation and appropriate antibiotic treatment.", "\ud83e\uddfc Practice strict hand hygiene, especially before eating and after using the toilet.", "\ud83e\udd64 Drink only purified or boiled water and avoid raw foods that may be contaminated.", "\ud83c\udf7d\ufe0f Avoid eating food from street vendors or restaurants with questionable hygiene practices.", "\ud83d\udeab Avoid preparing food for others until cleared by a healthcare provider to prevent spreading the infection."], "urgency_level": "Medium", "flagged_results": ["Widal Test (Typhoid)"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some results that should be discussed with a healthcare provider. Let me explain what these results mean and what steps you should consider taking.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-23 19:43:08,233 - root - INFO - Saved test results data for user 8d64d98d-ac41-4543-aaf8-655cc7cef69f
2025-05-23 19:47:29,776 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-23 19:47:29,783 - root - INFO - Retrieved context: ...
2025-05-23 19:47:29,785 - root - INFO - Added health data context to system prompt for user 8d64d98d-ac41-4543-aaf8-655cc7cef69f
2025-05-23 19:47:36,710 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-23 19:50:02,563 - root - INFO - Processing test results data: {"selected_tests": ["Widal Test (Typhoid)"], "test_name": "Widal Test", "patient_age": 40, "patient_sex": "Male", "test_result": "Positive", "widal": {"Typhi O": "Reactive", "Typhi H": "Reactive", "Paratyphi A,H": "Reactive", "Paratyphi B,H": "Reactive"}}
2025-05-23 19:50:02,573 - root - INFO - Test results interpretation: {"timestamp": "2025-05-23T19:50:02.572336", "tests_analyzed": ["Widal Test (Typhoid)"], "interpretation": ["Your Widal test shows reactivity to Typhi O, Typhi H, Paratyphi A,H, Paratyphi B,H.", "Reactivity to both Typhi O and Typhi H antigens strongly suggests typhoid fever infection. This indicates your immune system is responding to Salmonella Typhi bacteria.", "The O (somatic) antigen reactivity indicates current infection, while H (flagellar) antigen reactivity suggests your immune system has been mounting a response for some time.", "Reactivity to Paratyphi A,H, Paratyphi B,H suggests possible paratyphoid fever, caused by Salmonella Paratyphi. This illness is similar to typhoid but often presents with milder symptoms.", "Reactivity to multiple Paratyphi antigens suggests a significant infection that requires medical attention."], "recommendations": ["\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider within 24-48 hours for appropriate antibiotic treatment.", "\ud83e\ude7a Treatment typically includes antibiotics such as fluoroquinolones (e.g., ciprofloxacin), third-generation cephalosporins (e.g., ceftriaxone), or azithromycin.", "\ud83c\udf21\ufe0f Monitor your temperature every 4-6 hours. Fever in typhoid typically rises gradually and may reach 103-104\u00b0F (39-40\u00b0C).", "\ud83d\udca7 Maintain proper hydration with clean water or oral rehydration solution, especially if you have fever or diarrhea.", "\ud83d\udc68\u200d\u2695\ufe0f Consult a healthcare provider for evaluation and appropriate antibiotic treatment.", "\ud83e\uddfc Practice strict hand hygiene, especially before eating and after using the toilet.", "\ud83e\udd64 Drink only purified or boiled water and avoid raw foods that may be contaminated.", "\ud83c\udf7d\ufe0f Avoid eating food from street vendors or restaurants with questionable hygiene practices.", "\ud83d\udeab Avoid preparing food for others until cleared by a healthcare provider to prevent spreading the infection."], "urgency_level": "Medium", "flagged_results": ["Widal Test (Typhoid)"], "doctor_visit_recommended": true, "doctor_summary": "Hello! I've reviewed your test results, and here's what I'm seeing: I'm noticing some results that should be discussed with a healthcare provider. Let me explain what these results mean and what steps you should consider taking.", "identified_patterns": [], "pattern_confidence": 0.0}
2025-05-23 19:50:02,596 - root - INFO - Saved test results data for user efdc346c-925c-4e47-b94c-509d6183dc0a
2025-05-23 19:53:25,350 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-23 19:53:25,353 - root - INFO - Retrieved context: ...
2025-05-23 19:53:25,353 - root - INFO - Added health data context to system prompt for user efdc346c-925c-4e47-b94c-509d6183dc0a
2025-05-23 19:53:34,735 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-23 19:54:40,052 - root - INFO - Saved lipid profile data for user efdc346c-925c-4e47-b94c-509d6183dc0a
2025-05-23 19:58:50,087 - root - INFO - Saved kidney function data for user efdc346c-925c-4e47-b94c-509d6183dc0a
2025-05-23 20:02:22,890 - root - INFO - Saved symptom checker data for user efdc346c-925c-4e47-b94c-509d6183dc0a
2025-05-23 20:04:35,797 - root - INFO - Explaining lab test: malaria test
2025-05-28 12:27:26,382 - root - INFO - Successfully imported health tools
2025-05-28 12:27:26,385 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-28 12:27:26,386 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-28 12:27:26,452 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-28 12:27:26,453 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-28 12:27:26,454 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-28 12:27:26,533 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-28 12:32:57,825 - root - INFO - Saved kidney function data for user user_123
2025-05-28 12:41:55,669 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/embed "HTTP/1.1 200 OK"
2025-05-28 12:41:55,691 - root - INFO - Retrieved context: ...
2025-05-28 12:42:53,806 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-05-28 12:42:53,811 - root - INFO - Detected primary intent: lipid_profile with score 1.0
2025-05-28 12:44:33,271 - root - INFO - Returning default health data: {"Glucose": null, "SpO2": null, "ECG (Heart Rate)": null, "Blood Pressure (Systolic)": null, "Blood Pressure (Diastolic)": null, "Weight (BMI)": null, "Temperature": null, "Malaria": "Unknown", "Widal Test": "Unknown", "Hepatitis B": "Unknown", "Voluntary Serology": "Unknown", "Perfusion_index": null, "Waist Circumference": null, "Fev": null}
2025-05-28 12:46:40,944 - root - INFO - Saved symptom checker data for user user123
2025-05-29 15:09:58,517 - root - INFO - Successfully imported health tools
2025-05-29 15:09:58,531 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-29 15:09:58,549 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-29 15:09:58,637 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-29 15:09:58,656 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-29 15:09:58,658 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-29 15:09:58,720 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-29 15:16:15,835 - root - INFO - Processing mental health assessment for user 2af3403f-acbc-4fac-a47a-208de8063be1
2025-05-29 15:16:15,835 - root - INFO - Assessment data: {"age": 25, "gender": "Male", "country": "United States", "recent_stress_event": true, "stress_responses": {"medical": [3, 3, 3, 3, 3]}, "phq9_responses": [0, 0, 0, 0, 0, 0, 0, 0, 0], "gad7_responses": [0, 0, 0, 0, 0, 0, 0]}
2025-05-29 15:16:18,365 - tools.tools_mental_health_assessment - INFO - Mental health risk prediction model loaded successfully
2025-05-29 15:16:18,412 - root - INFO - Mental health assessment completed for user 2af3403f-acbc-4fac-a47a-208de8063be1
2025-05-29 15:31:55,021 - root - INFO - Processing mental health assessment for user 2af3403f-acbc-4fac-a47a-208de8063be1
2025-05-29 15:31:55,021 - root - INFO - Assessment data: {"age": 25, "gender": "Male", "country": "United States", "recent_stress_event": false, "stress_responses": {"relationship": [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]}, "phq9_responses": [0, 0, 0, 0, 0, 0, 0, 0, 0], "gad7_responses": [0, 0, 0, 0, 0, 0, 0]}
2025-05-29 15:31:55,153 - tools.tools_mental_health_assessment - INFO - Mental health risk prediction model loaded successfully
2025-05-29 15:31:55,219 - root - INFO - Mental health assessment completed for user 2af3403f-acbc-4fac-a47a-208de8063be1
2025-05-29 22:41:45,001 - root - INFO - Successfully imported health tools
2025-05-29 22:41:45,017 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-29 22:41:45,042 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-29 22:41:45,132 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-29 22:41:45,161 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-29 22:41:45,167 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-29 22:41:45,235 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-29 22:43:01,680 - root - INFO - Successfully imported health tools
2025-05-29 22:43:01,680 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-29 22:43:01,680 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-29 22:43:01,747 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-29 22:43:01,747 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-29 22:43:01,747 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-29 22:43:01,832 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-29 22:43:53,508 - root - INFO - Successfully imported health tools
2025-05-29 22:43:53,508 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-29 22:43:53,508 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-29 22:43:53,560 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-29 22:43:53,571 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-29 22:43:53,571 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-29 22:43:53,629 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-30 04:49:36,255 - root - INFO - Successfully imported health tools
2025-05-30 04:49:36,255 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-30 04:49:36,256 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-30 04:49:36,291 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-30 04:49:36,291 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-30 04:49:36,291 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-30 04:49:36,337 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-30 04:50:15,332 - root - INFO - Successfully imported health tools
2025-05-30 04:50:15,333 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-30 04:50:15,333 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-30 04:50:15,365 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-30 04:50:15,365 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-30 04:50:15,365 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-30 04:50:15,415 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-30 04:53:52,128 - root - ERROR - Stress assessment error: division by zero
2025-05-30 05:34:06,465 - root - INFO - Successfully imported health tools
2025-05-30 05:34:06,465 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-30 05:34:06,465 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-30 05:34:06,506 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-30 05:34:06,506 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-30 05:34:06,506 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-30 05:34:06,549 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-30 05:34:45,402 - root - INFO - Processing mental health assessment for user test_user_005
2025-05-30 05:34:45,402 - root - INFO - Assessment data: {"age": 45, "gender": "Male", "country": "Australia", "recent_stress_event": true, "stress_responses": {"medical": [4, 4, 3, 3, 4]}, "phq9_responses": [2, 2, 2, 3, 1, 1, 2, 0, 0], "gad7_responses": [2, 2, 2, 1, 1, 2, 2]}
2025-05-30 05:34:48,243 - tools.tools_mental_health_assessment - INFO - Mental health risk prediction model loaded successfully
2025-05-30 05:34:48,374 - root - INFO - Mental health assessment completed for user test_user_005
2025-05-30 05:37:31,582 - root - INFO - Processing mental health assessment for user test_user_001
2025-05-30 05:37:31,584 - root - INFO - Assessment data: {"age": 25, "gender": "Female", "country": "Nigeria", "recent_stress_event": false, "stress_responses": {"work": [2, 1, 2, 3, 2]}, "phq9_responses": [0, 1, 1, 2, 0, 0, 1, 0, 0], "gad7_responses": [1, 0, 1, 1, 0, 1, 0]}
2025-05-30 05:37:31,706 - tools.tools_mental_health_assessment - INFO - Mental health risk prediction model loaded successfully
2025-05-30 05:37:31,796 - root - INFO - Mental health assessment completed for user test_user_001
2025-05-30 05:46:33,704 - root - INFO - Processing mental health assessment for user test_user_003
2025-05-30 05:46:33,705 - root - INFO - Assessment data: {"age": 29, "gender": "Female", "country": "Canada", "recent_stress_event": true, "stress_responses": {"work": [4, 4, 4, 4, 3, 2, 4, 3, 2, 2], "relationship": [3, 3, 3, 3, 3, 2, 3, 3, 2, 2]}, "phq9_responses": [2, 2, 3, 3, 2, 2, 2, 1, 0], "gad7_responses": [2, 2, 3, 2, 2, 2, 2]}
2025-05-30 05:46:33,879 - tools.tools_mental_health_assessment - INFO - Mental health risk prediction model loaded successfully
2025-05-30 05:46:34,001 - root - INFO - Mental health assessment completed for user test_user_003
2025-05-30 05:55:50,060 - root - INFO - Successfully imported health tools
2025-05-30 05:55:50,063 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-30 05:55:50,063 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-30 05:55:50,103 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-30 05:55:50,113 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-30 05:55:50,114 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-30 05:55:50,169 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
2025-05-30 06:14:52,872 - root - INFO - Processing mental health assessment for user test_user_003
2025-05-30 06:14:52,872 - root - INFO - Assessment data: {"age": 29, "gender": "Female", "country": "Canada", "recent_stress_event": true, "stress_responses": {"work": [4, 4, 4, 4, 3, 2, 4, 3, 2, 2], "relationship": [3, 3, 3, 3, 3, 2, 3, 3, 2, 2]}, "phq9_responses": [2, 2, 3, 3, 2, 2, 2, 1, 0], "gad7_responses": [2, 2, 3, 2, 2, 2, 2]}
2025-05-30 06:14:52,981 - tools.tools_mental_health_assessment - INFO - Mental health risk prediction model loaded successfully
2025-05-30 06:14:53,078 - root - INFO - Mental health assessment completed for user test_user_003
2025-05-30 06:15:20,037 - root - INFO - Processing mental health assessment for user test_user_003
2025-05-30 06:15:20,037 - root - INFO - Assessment data: {"age": 29, "gender": "Female", "country": "Canada", "recent_stress_event": true, "stress_responses": {"work": [4, 4, 4, 4, 3, 2, 4, 3, 2, 2], "relationship": [3, 3, 3, 3, 3, 2, 3, 3, 2, 2]}, "phq9_responses": [2, 2, 3, 3, 2, 2, 2, 1, 0], "gad7_responses": [2, 2, 3, 2, 2, 2, 2]}
2025-05-30 06:15:20,173 - tools.tools_mental_health_assessment - INFO - Mental health risk prediction model loaded successfully
2025-05-30 06:15:20,258 - root - INFO - Mental health assessment completed for user test_user_003
2025-05-30 06:16:02,923 - root - INFO - Successfully imported health tools
2025-05-30 06:16:02,923 - root - INFO - ✅ FAISS index loaded for qwen2.5:1.5b
2025-05-30 06:16:02,925 - root - INFO - ✅ Metadata loaded for qwen2.5:1.5b
2025-05-30 06:16:02,956 - root - INFO - ✅ Embedding model loaded for qwen2.5:1.5b
2025-05-30 06:16:02,956 - root - INFO - ✅ FAISS index loaded for deepseek-r1:1.5b
2025-05-30 06:16:02,964 - root - INFO - ✅ Metadata loaded for deepseek-r1:1.5b
2025-05-30 06:16:03,005 - root - INFO - ✅ Embedding model loaded for deepseek-r1:1.5b
