import json
import joblib
import pandas as pd
import numpy as np
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MentalHealthAssessmentTool:
    """
    Comprehensive Mental Health Assessment Tool that includes:
    - Stress/Burnout Assessment (4 categories: work, school, relationship, medical)
    - PHQ-9 Depression Screening
    - GAD-7 Anxiety Screening
    - ML-based Mental Health Risk Prediction
    - Crisis Resource Recommendations
    """

    def __init__(self):
        self.model_path = os.path.join(os.path.dirname(__file__), 'mental_health_risk_predictor.pkl')
        self.model = None
        self._load_model()

        # Likert scale for stress/burnout assessment
        self.likert_scale = {
            1: "Never",
            2: "Rarely",
            3: "Sometimes",
            4: "Often",
            5: "Always"
        }

        # Question sets for stress/burnout assessment
        self.stress_questions = {
            "work": [
                "I feel overwhelmed by my job responsibilities.",
                "I struggle to complete tasks due to fatigue or mental exhaustion.",
                "I get fewer than 6 hours of sleep on most workdays.",
                "I rarely take breaks or rest during the workday.",
                "I feel emotionally detached from my work.",
                "I feel recognized and valued at my workplace.",
                "I work beyond 9 hours a day on a regular basis.",
                "I experience physical symptoms such as headaches, fatigue, or insomnia due to work.",
                "I feel like I have a healthy work-life balance.",
                "I enjoy going to work or feel a sense of purpose in my job."
            ],
            "school": [
                "I often feel anxious about deadlines and academic performance.",
                "I struggle to get 7–8 hours of sleep on school nights.",
                "I study or attend schoolwork for more than 8 hours daily.",
                "I feel unable to cope with academic pressure.",
                "I rarely take breaks or engage in non-academic hobbies.",
                "I feel emotionally supported by teachers or school counselors.",
                "I compare myself negatively to other students.",
                "I have trouble focusing and retaining what I study.",
                "I feel burnout from continuous academic demands.",
                "I believe I am managing school and personal life well."
            ],
            "relationship": [
                "I often feel emotionally drained by my relationships.",
                "I find myself avoiding conversations with people close to me.",
                "I feel like my needs are not being acknowledged or understood.",
                "I frequently have conflicts or unresolved tension with loved ones.",
                "I feel pressure to constantly give more than I receive.",
                "I receive emotional support from those close to me.",
                "I often feel lonely even when I am with others.",
                "I feel stressed by trying to maintain harmony in my relationships.",
                "I find joy and peace in my close connections.",
                "I have space to express myself honestly and without judgment."
            ],
            "medical": [
                "I frequently feel tired, even after resting.",
                "My medical condition affects my mood or productivity.",
                "I worry about my health status or future frequently.",
                "I find it hard to manage medication or treatment schedules.",
                "I experience sleep difficulties due to my health issues.",
                "I feel emotionally supported by my healthcare providers.",
                "My health limits my ability to participate in daily activities.",
                "I feel frustrated or helpless about my health condition.",
                "I avoid seeking help even when my symptoms worsen.",
                "I feel in control of my health and wellness decisions."
            ]
        }

        # PHQ-9 Depression Screening Questions
        self.phq9_questions = [
            "Little interest or pleasure in doing things",
            "Feeling down, depressed, or hopeless",
            "Trouble falling/staying asleep, or sleeping too much",
            "Feeling tired or having little energy",
            "Poor appetite or overeating",
            "Feeling bad about yourself - or that you're a failure",
            "Trouble concentrating on things",
            "Moving/speaking slowly or being fidgety/restless",
            "Thoughts of self-harm or suicide"
        ]

        # GAD-7 Anxiety Screening Questions
        self.gad7_questions = [
            "Feeling nervous, anxious, or on edge",
            "Not being able to stop worrying",
            "Worrying too much about different things",
            "Trouble relaxing",
            "Being so restless that it's hard to sit still",
            "Becoming easily annoyed or irritable",
            "Feeling afraid as if something awful might happen"
        ]

        # Crisis resources by country
        self.crisis_resources = {
            "United States": {
                "hotline": "988 (Suicide & Crisis Lifeline)",
                "text": "Text HOME to 741741 (Crisis Text Line)",
                "website": "https://www.mentalhealth.gov/get-help/immediate-help"
            },
            "United Kingdom": {
                "hotline": "116 123 (Samaritans)",
                "website": "https://www.samaritans.org"
            },
            "Canada": {
                "hotline": "1-************ (Crisis Services Canada)",
                "website": "https://www.crisisservicescanada.ca"
            },
            "Australia": {
                "hotline": "13 11 14 (Lifeline Australia)",
                "website": "https://www.lifeline.org.au"
            }
        }

    def _load_model(self):
        """Load the trained mental health risk prediction model"""
        try:
            if os.path.exists(self.model_path):
                self.model = joblib.load(self.model_path)
                logger.info("Mental health risk prediction model loaded successfully")
            else:
                logger.warning(f"Model file not found at {self.model_path}")
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            self.model = None

    def interpret_stress_score(self, score: int, max_score: int) -> str:
        """Interpret stress/burnout score"""
        percentage = (score / max_score) * 100
        if percentage <= 50:
            return "🟢 Low stress/burnout"
        elif 51 <= percentage <= 70:
            return "🟡 Moderate stress/burnout"
        else:
            return "🔴 High stress/burnout – consider seeking support"

    def assess_stress_burnout(self, responses: Dict[str, List[int]]) -> Dict[str, Any]:
        """Assess stress and burnout across multiple categories"""
        results = {}

        for category, category_responses in responses.items():
            if category in self.stress_questions:
                total_score = sum(category_responses)
                max_score = len(category_responses) * 5
                percentage = round((total_score / max_score) * 100, 2)
                interpretation = self.interpret_stress_score(total_score, max_score)

                results[category] = {
                    "total_score": total_score,
                    "max_score": max_score,
                    "percentage": percentage,
                    "interpretation": interpretation
                }

        # Calculate overall average if multiple categories
        if len(results) > 1:
            avg_percentage = round(sum(r["percentage"] for r in results.values()) / len(results), 2)
            if avg_percentage <= 50:
                avg_interpretation = "🟢 Low stress/burnout"
            elif 51 <= avg_percentage <= 70:
                avg_interpretation = "🟡 Moderate stress/burnout"
            else:
                avg_interpretation = "🔴 High stress/burnout – consider seeking support"

            results["overall"] = {
                "average_percentage": avg_percentage,
                "interpretation": avg_interpretation
            }

        return results

    def assess_phq9(self, responses: List[int]) -> Dict[str, Any]:
        """Assess depression using PHQ-9 scale"""
        total_score = sum(responses)

        if total_score <= 4:
            severity = "Minimal depression"
            recommendation = "Monitor symptoms and maintain healthy lifestyle habits"
        elif total_score <= 9:
            severity = "Mild depression"
            recommendation = "Consider lifestyle changes and monitor symptoms closely"
        elif total_score <= 14:
            severity = "Moderate depression"
            recommendation = "Consider professional consultation and treatment options"
        elif total_score <= 19:
            severity = "Moderately severe depression"
            recommendation = "Professional treatment is recommended"
        else:
            severity = "Severe depression"
            recommendation = "Immediate professional intervention is strongly recommended"

        return {
            "total_score": total_score,
            "max_score": 27,
            "severity": severity,
            "recommendation": recommendation
        }

    def assess_gad7(self, responses: List[int]) -> Dict[str, Any]:
        """Assess anxiety using GAD-7 scale"""
        total_score = sum(responses)

        if total_score <= 4:
            severity = "Minimal anxiety"
            recommendation = "Continue current coping strategies"
        elif total_score <= 9:
            severity = "Mild anxiety"
            recommendation = "Consider stress management techniques"
        elif total_score <= 14:
            severity = "Moderate anxiety"
            recommendation = "Consider professional consultation"
        else:
            severity = "Severe anxiety"
            recommendation = "Professional treatment is recommended"

        return {
            "total_score": total_score,
            "max_score": 21,
            "severity": severity,
            "recommendation": recommendation
        }

    def predict_mental_health_risk(self, age: int, gender: str, recent_stress_event: bool,
                                 phq9_responses: List[int], gad7_responses: List[int]) -> Dict[str, Any]:
        """Predict mental health risk using the trained ML model"""
        if self.model is None:
            return {
                "error": "Mental health risk prediction model not available",
                "risk_level": "Unable to assess",
                "confidence": 0.0
            }

        try:
            # Prepare data for prediction
            data = {
                'age': [age],
                'gender': [gender],
                'recent_stress_event': [1 if recent_stress_event else 0]
            }

            # Add PHQ-9 responses
            for i, response in enumerate(phq9_responses, 1):
                data[f'phq_q{i}'] = [response]

            # Add GAD-7 responses
            for i, response in enumerate(gad7_responses, 1):
                data[f'gad_q{i}'] = [response]

            # Create DataFrame
            df = pd.DataFrame(data)

            # Make prediction
            prediction = self.model.predict(df)[0]
            prediction_proba = self.model.predict_proba(df)[0]

            # Interpret results
            risk_level = "High Risk" if prediction == 1 else "Low Risk"
            confidence = max(prediction_proba) * 100

            return {
                "risk_level": risk_level,
                "confidence": round(confidence, 2),
                "prediction": int(prediction),
                "probabilities": {
                    "low_risk": round(prediction_proba[0] * 100, 2),
                    "high_risk": round(prediction_proba[1] * 100, 2)
                }
            }

        except Exception as e:
            logger.error(f"Error in mental health risk prediction: {str(e)}")
            return {
                "error": f"Prediction error: {str(e)}",
                "risk_level": "Unable to assess",
                "confidence": 0.0
            }

    def get_crisis_resources(self, country: str = "United States") -> Dict[str, str]:
        """Get crisis intervention resources for a specific country"""
        return self.crisis_resources.get(country, self.crisis_resources["United States"])

    def generate_recommendations(self, stress_results: Dict, phq9_results: Dict,
                               gad7_results: Dict, risk_prediction: Dict) -> List[str]:
        """Generate personalized mental health recommendations"""
        recommendations = []

        # Stress/burnout recommendations
        if "overall" in stress_results:
            avg_percentage = stress_results["overall"]["average_percentage"]
            if avg_percentage > 70:
                recommendations.extend([
                    "🧘‍♀️ Practice daily stress management techniques (meditation, deep breathing)",
                    "⏰ Establish better work-life boundaries and time management",
                    "💤 Prioritize 7-9 hours of quality sleep each night",
                    "🤝 Consider speaking with a mental health professional"
                ])
            elif avg_percentage > 50:
                recommendations.extend([
                    "🌱 Incorporate regular relaxation activities into your routine",
                    "🚶‍♀️ Engage in regular physical exercise or movement",
                    "📱 Consider using stress management apps or tools"
                ])

        # Depression recommendations
        if phq9_results["total_score"] > 9:
            recommendations.extend([
                "☀️ Maintain a regular daily routine and sleep schedule",
                "🏃‍♀️ Engage in regular physical activity, even light exercise",
                "👥 Stay connected with supportive friends and family",
                "🎯 Set small, achievable daily goals"
            ])

        # Anxiety recommendations
        if gad7_results["total_score"] > 9:
            recommendations.extend([
                "🧠 Practice mindfulness and grounding techniques",
                "📝 Try journaling to process worries and thoughts",
                "🎵 Use relaxation techniques like progressive muscle relaxation",
                "⚡ Limit caffeine and alcohol intake"
            ])

        # High-risk recommendations
        if risk_prediction.get("risk_level") == "High Risk":
            recommendations.extend([
                "🚨 Consider scheduling an appointment with a mental health professional",
                "📞 Keep crisis hotline numbers readily available",
                "🛡️ Develop a safety plan with trusted friends or family",
                "💊 Discuss treatment options with a healthcare provider"
            ])

        # Remove duplicates while preserving order
        seen = set()
        unique_recommendations = []
        for rec in recommendations:
            if rec not in seen:
                seen.add(rec)
                unique_recommendations.append(rec)

        return unique_recommendations[:8]  # Limit to 8 recommendations

    def generate_follow_up_reminders(self, stress_results: Dict, phq9_results: Dict,
                                   gad7_results: Dict, risk_prediction: Dict) -> List[str]:
        """Generate follow-up reminders based on assessment results"""
        reminders = []

        # High-risk follow-up
        if risk_prediction.get("risk_level") == "High Risk":
            reminders.append("📅 Schedule a mental health professional consultation within 1-2 weeks")
            reminders.append("🔄 Retake this assessment in 2 weeks to monitor progress")

        # Moderate to severe symptoms
        elif (phq9_results["total_score"] > 14 or gad7_results["total_score"] > 14 or
              (stress_results.get("overall", {}).get("average_percentage", 0) > 70)):
            reminders.append("📋 Consider professional consultation within 2-4 weeks")
            reminders.append("📊 Retake this assessment in 3-4 weeks")

        # Mild to moderate symptoms
        elif (phq9_results["total_score"] > 4 or gad7_results["total_score"] > 4 or
              (stress_results.get("overall", {}).get("average_percentage", 0) > 50)):
            reminders.append("🔍 Monitor symptoms and retake assessment in 4-6 weeks")
            reminders.append("📈 Track daily mood and stress levels")

        # General wellness
        else:
            reminders.append("✅ Continue current wellness practices")
            reminders.append("🔄 Consider retaking this assessment in 2-3 months for wellness monitoring")

        return reminders

    def comprehensive_assessment(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive mental health assessment"""
        try:
            # Extract user data
            age = user_data.get("age", 25)
            gender = user_data.get("gender", "Other")
            recent_stress_event = user_data.get("recent_stress_event", False)

            # Assessment responses
            stress_responses = user_data.get("stress_responses", {})
            phq9_responses = user_data.get("phq9_responses", [0] * 9)
            gad7_responses = user_data.get("gad7_responses", [0] * 7)

            # Perform assessments
            stress_results = self.assess_stress_burnout(stress_responses) if stress_responses else {}
            phq9_results = self.assess_phq9(phq9_responses)
            gad7_results = self.assess_gad7(gad7_responses)

            # ML risk prediction
            risk_prediction = self.predict_mental_health_risk(
                age, gender, recent_stress_event, phq9_responses, gad7_responses
            )

            # Generate recommendations and follow-up
            recommendations = self.generate_recommendations(
                stress_results, phq9_results, gad7_results, risk_prediction
            )
            follow_up_reminders = self.generate_follow_up_reminders(
                stress_results, phq9_results, gad7_results, risk_prediction
            )

            # Get crisis resources
            country = user_data.get("country", "United States")
            crisis_resources = self.get_crisis_resources(country)

            # Compile results
            assessment_results = {
                "timestamp": datetime.now().isoformat(),
                "user_info": {
                    "age": age,
                    "gender": gender,
                    "recent_stress_event": recent_stress_event,
                    "country": country
                },
                "assessments": {
                    "stress_burnout": stress_results,
                    "depression_phq9": phq9_results,
                    "anxiety_gad7": gad7_results,
                    "ml_risk_prediction": risk_prediction
                },
                "recommendations": recommendations,
                "follow_up_reminders": follow_up_reminders,
                "crisis_resources": crisis_resources,
                "summary": self._generate_summary(stress_results, phq9_results, gad7_results, risk_prediction)
            }

            return assessment_results

        except Exception as e:
            logger.error(f"Error in comprehensive assessment: {str(e)}")
            return {
                "error": f"Assessment error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

    def _generate_summary(self, stress_results: Dict, phq9_results: Dict,
                         gad7_results: Dict, risk_prediction: Dict) -> str:
        """Generate a summary of the mental health assessment"""
        summary_parts = []

        # Overall risk level
        risk_level = risk_prediction.get("risk_level", "Unable to assess")
        confidence = risk_prediction.get("confidence", 0)
        summary_parts.append(f"🎯 **Overall Mental Health Risk**: {risk_level} (Confidence: {confidence}%)")

        # Depression assessment
        phq9_severity = phq9_results.get("severity", "Unknown")
        phq9_score = phq9_results.get("total_score", 0)
        summary_parts.append(f"😔 **Depression (PHQ-9)**: {phq9_severity} (Score: {phq9_score}/27)")

        # Anxiety assessment
        gad7_severity = gad7_results.get("severity", "Unknown")
        gad7_score = gad7_results.get("total_score", 0)
        summary_parts.append(f"😰 **Anxiety (GAD-7)**: {gad7_severity} (Score: {gad7_score}/21)")

        # Stress/burnout assessment
        if "overall" in stress_results:
            avg_percentage = stress_results["overall"]["average_percentage"]
            interpretation = stress_results["overall"]["interpretation"]
            summary_parts.append(f"🔥 **Stress/Burnout**: {interpretation} ({avg_percentage}%)")

        return "\n".join(summary_parts)
